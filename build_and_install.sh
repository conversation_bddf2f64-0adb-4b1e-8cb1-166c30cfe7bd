#!/bin/bash

echo "🔨 Building Flutter app with TensorFlow Lite fixes..."

# Navigate to project directory
cd /home/<USER>/Documents/Projects/FlutterApp

# Check if flutter is available
if command -v flutter &> /dev/null; then
    echo "✅ Flutter found, using flutter commands"
    
    # Clean previous builds
    echo "🧹 Cleaning previous builds..."
    flutter clean
    
    # Get dependencies
    echo "📦 Getting dependencies..."
    flutter pub get
    
    # Build APK
    echo "🔨 Building APK..."
    flutter build apk --release
    
    # Install APK
    echo "📱 Installing APK..."
    adb install build/app/outputs/flutter-apk/app-release.apk
    
else
    echo "⚠️ Flutter not found in PATH"
    echo "🔍 Looking for existing APK..."
    
    if [ -f "build/app/outputs/flutter-apk/app-release.apk" ]; then
        echo "📱 Installing existing APK..."
        adb install build/app/outputs/flutter-apk/app-release.apk
    else
        echo "❌ No APK found. Please build the app manually."
        echo "💡 Try running these commands manually:"
        echo "   flutter clean"
        echo "   flutter pub get"
        echo "   flutter build apk --release"
        echo "   adb install build/app/outputs/flutter-apk/app-release.apk"
    fi
fi

echo "✅ Build and install script completed"
