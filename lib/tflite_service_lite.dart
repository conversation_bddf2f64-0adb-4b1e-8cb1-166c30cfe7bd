import 'package:flutter/services.dart';
import 'package:tflite_flutter/tflite_flutter.dart';
import 'dart:typed_data';
import 'dart:math';

class TFLiteServiceLite {
  Interpreter? _interpreter;
  bool _isModelLoaded = false;

  bool get isModelLoaded => _isModelLoaded;

  Future<bool> loadModel() async {
    try {
      print('🤖 Loading TFLite model...');
      
      // Load the model from assets
      final ByteData modelBytes = await rootBundle.load('assets/models/gesture_model1.tflite');
      final Uint8List modelData = modelBytes.buffer.asUint8List();
      
      // Create interpreter with minimal options for compatibility
      _interpreter = Interpreter.fromBuffer(modelData);
      
      // Test the model with dummy data to ensure it works
      await _testModel();
      
      _isModelLoaded = true;
      print('✅ TFLite model loaded successfully');
      return true;
      
    } catch (e) {
      print('❌ Error loading TFLite model: $e');
      _isModelLoaded = false;
      return false;
    }
  }

  Future<void> _testModel() async {
    if (_interpreter == null) return;
    
    try {
      // Create dummy input data for testing
      var input = List.generate(1, (i) => List.generate(100, (j) => List.generate(3, (k) => 0.0)));
      var output = List.generate(1, (i) => List.generate(11, (j) => 0.0));
      
      // Run a test inference
      _interpreter!.run(input, output);
      print('✅ Model test successful');
    } catch (e) {
      print('⚠️ Model test failed: $e');
      throw Exception('Model test failed: $e');
    }
  }

  List<double>? runInference(List<List<double>> points) {
    if (!_isModelLoaded || _interpreter == null) {
      print('⚠️ Model not loaded');
      return null;
    }

    try {
      // Use NRDP to normalize input to exactly 100 points
      List<List<double>> processedPoints = nrdp(points, 100);

      // Ensure we have exactly 100 points with 3 dimensions each
      while (processedPoints.length < 100) {
        processedPoints.add([0.0, 0.0, 0.0]);
      }

      // Ensure each point has exactly 3 dimensions (x, y, time)
      for (int i = 0; i < processedPoints.length; i++) {
        while (processedPoints[i].length < 3) {
          processedPoints[i].add(i / 100.0); // Add time index if missing
        }
        if (processedPoints[i].length > 3) {
          processedPoints[i] = processedPoints[i].sublist(0, 3);
        }
      }

      // Create input tensor [1, 100, 3]
      var input = [processedPoints];

      // Create output tensor [1, 11] for 11 classes (0-9 + ♡)
      var output = [List.filled(11, 0.0)];

      // Run inference
      _interpreter!.run(input, output);

      // Return the output probabilities
      return output[0];

    } catch (e) {
      print('❌ Inference error: $e');
      return null;
    }
  }

  void close() {
    try {
      _interpreter?.close();
      _interpreter = null;
      _isModelLoaded = false;
      print('🔒 TFLite model closed');
    } catch (e) {
      print('⚠️ Error closing model: $e');
    }
  }
}

// RDP (Ramer-Douglas-Peucker) algorithm implementation
List<List<double>> rdp(List<List<double>> points, double epsilon) {
  if (points.length <= 2) return points;

  List<double> start = points[0];
  List<double> end = points[points.length - 1];

  double maxDistance = 0;
  int maxIndex = 0;

  for (int i = 1; i < points.length - 1; i++) {
    double distance = perpendicularDistance(points[i], start, end);
    if (distance > maxDistance) {
      maxDistance = distance;
      maxIndex = i;
    }
  }

  if (maxDistance > epsilon) {
    List<List<double>> left = rdp(points.sublist(0, maxIndex + 1), epsilon);
    List<List<double>> right = rdp(points.sublist(maxIndex), epsilon);
    return [...left.sublist(0, left.length - 1), ...right];
  }

  return [start, end];
}

// Calculate perpendicular distance from point to line
double perpendicularDistance(List<double> point, List<double> lineStart, List<double> lineEnd) {
  double x0 = point[0], y0 = point[1];
  double x1 = lineStart[0], y1 = lineStart[1];
  double x2 = lineEnd[0], y2 = lineEnd[1];

  double numerator = ((y2 - y1) * x0 - (x2 - x1) * y0 + x2 * y1 - y2 * x1).abs();
  double denominator = sqrt(pow(y2 - y1, 2) + pow(x2 - x1, 2));

  return denominator == 0 ? 0 : numerator / denominator;
}

// Interpolate points to exact number
List<List<double>> interpolate(List<List<double>> points, int targetCount) {
  if (points.length <= 1) return points;
  if (points.length == targetCount) return points;

  List<List<double>> result = [points[0]];
  double step = (points.length - 1) / (targetCount - 1);

  for (int i = 1; i < targetCount - 1; i++) {
    double index = i * step;
    int lowerIndex = index.floor();
    int upperIndex = index.ceil();
    double t = index - lowerIndex;

    if (lowerIndex == upperIndex) {
      result.add([...points[lowerIndex]]);
    } else {
      List<double> interpolated = [];
      for (int j = 0; j < points[lowerIndex].length; j++) {
        double value = (1 - t) * points[lowerIndex][j] + t * points[upperIndex][j];
        interpolated.add(value);
      }
      result.add(interpolated);
    }
  }

  result.add([...points[points.length - 1]]);
  return result;
}

// Normalize array to [0,1] range
List<List<double>> normalizeArray(List<List<double>> points) {
  if (points.isEmpty) return points;

  double minX = double.infinity, maxX = double.negativeInfinity;
  double minY = double.infinity, maxY = double.negativeInfinity;

  for (var point in points) {
    if (point.length >= 2) {
      if (point[0] < minX) minX = point[0];
      if (point[0] > maxX) maxX = point[0];
      if (point[1] < minY) minY = point[1];
      if (point[1] > maxY) maxY = point[1];
    }
  }

  return points.map((point) {
    if (point.length >= 2) {
      double normalizedX = maxX != minX ? (point[0] - minX) / (maxX - minX) : 0.0;
      double normalizedY = maxY != minY ? (point[1] - minY) / (maxY - minY) : 0.0;
      return [normalizedX, normalizedY, ...point.sublist(2)];
    }
    return [...point];
  }).toList();
}

// Adjust epsilon to get target number of points
Map<String, dynamic> adjustEpsilon(List<List<double>> points, int targetCount) {
  double minEpsilon = 0.0;
  double maxEpsilon = 0.0;

  // Find max distance to set initial range
  for (int i = 1; i < points.length - 1; i++) {
    double distance = perpendicularDistance(points[i], points[0], points[points.length - 1]);
    if (distance > maxEpsilon) maxEpsilon = distance;
  }

  double epsilon = (minEpsilon + maxEpsilon) / 2;
  List<List<double>> result = [];

  while (minEpsilon <= maxEpsilon) {
    result = rdp(points, epsilon);

    if (result.length == targetCount) break;

    if (result.length > targetCount) {
      minEpsilon = epsilon + double.minPositive;
    } else {
      maxEpsilon = epsilon - double.minPositive;
    }

    epsilon = (minEpsilon + maxEpsilon) / 2;

    if ((maxEpsilon - minEpsilon).abs() < double.minPositive) break;
  }

  return {'epsilon': epsilon, 'points': result};
}

// Main NRDP function - Normalized RDP with target count
List<List<double>> nrdp(List<List<double>> points, [int targetCount = 100]) {
  if (points.isEmpty) return [];

  List<List<double>> result;

  if (points.length > targetCount) {
    Map<String, dynamic> adjusted = adjustEpsilon(points, targetCount);
    result = adjusted['points'];

    if (result.length != targetCount) {
      result = interpolate(result, targetCount);
    }
  } else {
    result = interpolate(points, targetCount);
  }

  return normalizeArray(result);
}
