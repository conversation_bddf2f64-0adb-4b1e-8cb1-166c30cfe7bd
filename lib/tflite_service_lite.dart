import 'package:flutter/services.dart';
import 'package:tflite_flutter/tflite_flutter.dart';
import 'dart:typed_data';

class TFLiteServiceLite {
  Interpreter? _interpreter;
  bool _isModelLoaded = false;

  bool get isModelLoaded => _isModelLoaded;

  Future<bool> loadModel() async {
    try {
      print('🤖 Loading TFLite model...');
      
      // Load the model from assets
      final ByteData modelBytes = await rootBundle.load('assets/models/gesture_model1.tflite');
      final Uint8List modelData = modelBytes.buffer.asUint8List();
      
      // Create interpreter with minimal options for compatibility
      _interpreter = Interpreter.fromBuffer(modelData);
      
      // Test the model with dummy data to ensure it works
      await _testModel();
      
      _isModelLoaded = true;
      print('✅ TFLite model loaded successfully');
      return true;
      
    } catch (e) {
      print('❌ Error loading TFLite model: $e');
      _isModelLoaded = false;
      return false;
    }
  }

  Future<void> _testModel() async {
    if (_interpreter == null) return;
    
    try {
      // Create dummy input data for testing
      var input = List.generate(1, (i) => List.generate(100, (j) => List.generate(3, (k) => 0.0)));
      var output = List.generate(1, (i) => List.generate(11, (j) => 0.0));
      
      // Run a test inference
      _interpreter!.run(input, output);
      print('✅ Model test successful');
    } catch (e) {
      print('⚠️ Model test failed: $e');
      throw Exception('Model test failed: $e');
    }
  }

  List<double>? runInference(List<List<double>> points) {
    if (!_isModelLoaded || _interpreter == null) {
      print('⚠️ Model not loaded');
      return null;
    }

    try {
      // Prepare input data - limit to 100 points max for performance
      List<List<double>> processedPoints = points.length > 100 
          ? points.sublist(0, 100) 
          : points;
      
      // Pad to exactly 100 points if needed
      while (processedPoints.length < 100) {
        processedPoints.add([0.0, 0.0, 0.0]);
      }
      
      // Create input tensor [1, 100, 3]
      var input = [processedPoints];
      
      // Create output tensor [1, 11] for 11 classes (0-9 + ♡)
      var output = [List.filled(11, 0.0)];
      
      // Run inference
      _interpreter!.run(input, output);
      
      // Return the output probabilities
      return output[0];
      
    } catch (e) {
      print('❌ Inference error: $e');
      return null;
    }
  }

  void close() {
    try {
      _interpreter?.close();
      _interpreter = null;
      _isModelLoaded = false;
      print('🔒 TFLite model closed');
    } catch (e) {
      print('⚠️ Error closing model: $e');
    }
  }
}
