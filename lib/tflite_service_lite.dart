import 'package:flutter/services.dart';
import 'package:tflite_flutter/tflite_flutter.dart';
import 'dart:typed_data';
import 'dart:math';

class TFLiteServiceLite {
  Interpreter? _interpreter;
  bool _isModelLoaded = false;

  bool get isModelLoaded => _isModelLoaded;

  String _getAvailableMemory() {
    try {
      // This is a simple approximation
      return "Memory info not available in Flutter";
    } catch (e) {
      return "Unknown";
    }
  }

  Future<bool> loadModel() async {
    try {
      print('🤖 [VERBOSE] Starting TFLite model loading process...');
      print('🤖 [VERBOSE] Current time: ${DateTime.now()}');
      print('🤖 [VERBOSE] Model loaded flag before: $_isModelLoaded');

      // Check if TensorFlow Lite is available
      try {
        // Try to create a simple interpreter first to test if TFLite works
        print('🔍 [VERBOSE] Testing TensorFlow Lite availability...');
        print('🔍 [VERBOSE] Checking if tflite_flutter plugin is accessible...');

        // Check if model file exists
        print('📂 [VERBOSE] Attempting to load model from assets...');
        print('📂 [VERBOSE] Asset path: assets/models/gesture_model1.tflite');

        final ByteData modelBytes = await rootBundle.load('assets/models/gesture_model1.tflite');
        print('📁 [VERBOSE] Model file loaded successfully!');
        print('📁 [VERBOSE] File size: ${modelBytes.lengthInBytes} bytes');
        print('📁 [VERBOSE] File size in KB: ${(modelBytes.lengthInBytes / 1024).toStringAsFixed(2)} KB');

        if (modelBytes.lengthInBytes == 0) {
          throw Exception('Model file is empty');
        }

        if (modelBytes.lengthInBytes < 1000) {
          print('⚠️ [VERBOSE] Warning: Model file seems very small (${modelBytes.lengthInBytes} bytes)');
        }

        final Uint8List modelData = modelBytes.buffer.asUint8List();
        print('🔄 [VERBOSE] Model data converted to Uint8List');
        print('🔄 [VERBOSE] Uint8List length: ${modelData.length}');
        print('🔄 [VERBOSE] First 10 bytes: ${modelData.take(10).toList()}');

        // Create interpreter with multiple fallback strategies
        print('🔧 [VERBOSE] Creating TFLite interpreter...');
        print('🔧 [VERBOSE] Available memory: ${_getAvailableMemory()}');
        Exception? lastError;

        // Strategy 1: Default options
        try {
          print('🔧 [VERBOSE] Strategy 1: Trying default interpreter options...');
          print('🔧 [VERBOSE] Calling Interpreter.fromBuffer with ${modelData.length} bytes...');

          _interpreter = Interpreter.fromBuffer(modelData);

          print('✅ [VERBOSE] Interpreter created successfully with default options!');
          print('✅ [VERBOSE] Interpreter object: $_interpreter');
        } catch (e1) {
          lastError = e1 is Exception ? e1 : Exception(e1.toString());
          print('⚠️ [VERBOSE] Default interpreter failed with error: $e1');
          print('⚠️ [VERBOSE] Error type: ${e1.runtimeType}');
          print('⚠️ [VERBOSE] Error details: ${e1.toString()}');

          // Strategy 2: Custom options with minimal configuration
          try {
            print('🔧 [VERBOSE] Strategy 2: Trying custom interpreter options...');
            print('🔧 [VERBOSE] Creating InterpreterOptions with threads=1...');

            final options = InterpreterOptions()
              ..threads = 1;
            print('🔧 [VERBOSE] InterpreterOptions created: $options');
            print('🔧 [VERBOSE] Calling Interpreter.fromBuffer with options...');

            _interpreter = Interpreter.fromBuffer(modelData, options: options);

            print('✅ [VERBOSE] Interpreter created successfully with custom options!');
            print('✅ [VERBOSE] Interpreter object: $_interpreter');
          } catch (e2) {
            lastError = e2 is Exception ? e2 : Exception(e2.toString());
            print('⚠️ [VERBOSE] Custom interpreter failed with error: $e2');
            print('⚠️ [VERBOSE] Error type: ${e2.runtimeType}');
            print('⚠️ [VERBOSE] Error details: ${e2.toString()}');

            // Strategy 3: Minimal options with different thread count
            try {
              print('🔧 [VERBOSE] Strategy 3: Trying minimal interpreter options...');
              print('🔧 [VERBOSE] Creating InterpreterOptions with threads=2...');

              final options = InterpreterOptions()..threads = 2;
              print('🔧 [VERBOSE] InterpreterOptions created: $options');
              print('🔧 [VERBOSE] Calling Interpreter.fromBuffer with options...');

              _interpreter = Interpreter.fromBuffer(modelData, options: options);

              print('✅ [VERBOSE] Interpreter created successfully with minimal options!');
              print('✅ [VERBOSE] Interpreter object: $_interpreter');
            } catch (e3) {
              lastError = e3 is Exception ? e3 : Exception(e3.toString());
              print('❌ [VERBOSE] All interpreter creation strategies failed!');
              print('❌ [VERBOSE] Final error: $e3');
              print('❌ [VERBOSE] Final error type: ${e3.runtimeType}');
              print('❌ [VERBOSE] Final error details: ${e3.toString()}');
              throw lastError;
            }
          }
        }

        // Get model info if interpreter was created successfully
        if (_interpreter != null) {
          try {
            final inputTensors = _interpreter!.getInputTensors();
            final outputTensors = _interpreter!.getOutputTensors();
            print('📊 Input tensors: ${inputTensors.length}');
            print('📊 Output tensors: ${outputTensors.length}');

            if (inputTensors.isNotEmpty) {
              print('📊 Input shape: ${inputTensors[0].shape}');
              print('📊 Input type: ${inputTensors[0].type}');
            }

            if (outputTensors.isNotEmpty) {
              print('📊 Output shape: ${outputTensors[0].shape}');
              print('📊 Output type: ${outputTensors[0].type}');
            }
          } catch (infoError) {
            print('⚠️ Could not get model info: $infoError');
          }
        }

        // Mark as loaded first, then test
        print('🏁 [VERBOSE] Setting model loaded flag to true...');
        _isModelLoaded = true;
        print('🏁 [VERBOSE] Model loaded flag set: $_isModelLoaded');
        print('✅ [VERBOSE] TFLite model loaded successfully!');

        // Test the model with dummy data to ensure it works
        print('🧪 [VERBOSE] Testing model with dummy data...');
        print('🧪 [VERBOSE] Calling _testModel()...');
        try {
          await _testModel();
          print('✅ [VERBOSE] Model test passed - TensorFlow Lite is fully functional!');
          print('✅ [VERBOSE] Model is ready for inference');
        } catch (testError) {
          print('⚠️ [VERBOSE] Model test failed but interpreter was created: $testError');
          print('⚠️ [VERBOSE] Test error type: ${testError.runtimeType}');
          print('⚠️ [VERBOSE] Test error details: ${testError.toString()}');
          print('💡 [VERBOSE] Model may still work with real data');
          // Keep model loaded even if test fails - might work with real data
        }

        print('🎯 [VERBOSE] Returning true - model loading completed successfully');
        return true;

      } catch (assetError) {
        print('❌ [VERBOSE] Error loading model asset: $assetError');
        print('❌ [VERBOSE] Asset error type: ${assetError.runtimeType}');
        print('❌ [VERBOSE] Asset error details: ${assetError.toString()}');
        throw assetError;
      }

    } catch (e) {
      print('❌ [VERBOSE] Error loading TFLite model: $e');
      print('❌ [VERBOSE] Error type: ${e.runtimeType}');
      print('❌ [VERBOSE] Error details: ${e.toString()}');
      print('❌ [VERBOSE] Stack trace: ${StackTrace.current}');

      _isModelLoaded = false;
      print('❌ [VERBOSE] Model loaded flag set to: $_isModelLoaded');

      // Provide specific error information and suggestions
      String errorStr = e.toString().toLowerCase();
      if (errorStr.contains('libtensorflowlite_c.so') || errorStr.contains('dynamic library')) {
        print('💡 [VERBOSE] TensorFlow Lite native library issue detected.');
        print('💡 [VERBOSE] This is the main issue preventing model loading');
        print('🔧 [VERBOSE] Possible solutions:');
        print('   1. Update tflite_flutter plugin to latest version');
        print('   2. Add NDK configuration in android/app/build.gradle');
        print('   3. Clean and rebuild the project');
        print('   4. Check if device architecture is supported');
        print('📱 [VERBOSE] Falling back to simulation mode for now');
      } else if (errorStr.contains('tflite') || errorStr.contains('interpreter')) {
        print('💡 [VERBOSE] TensorFlow Lite interpreter issue detected.');
        print('🔧 [VERBOSE] This might be due to:');
        print('   - Incompatible model format');
        print('   - Plugin initialization failure');
        print('   - Device compatibility issues');
      } else {
        print('💡 [VERBOSE] Unknown error type detected.');
        print('💡 [VERBOSE] This is an unexpected error');
      }

      print('🔄 [VERBOSE] Returning false - model loading failed');
      return false;
    }
  }

  Future<void> _testModel() async {
    if (_interpreter == null) return;

    try {
      // Create dummy input data for testing [1, 100, 3]
      var input = List.generate(1, (i) => List.generate(100, (j) => List.generate(3, (k) => 0.5)));
      var output = List.generate(1, (i) => List.generate(11, (j) => 0.0));

      print('🧪 Input shape: [${input.length}, ${input[0].length}, ${input[0][0].length}]');
      print('🧪 Output shape: [${output.length}, ${output[0].length}]');

      // Run a test inference
      _interpreter!.run(input, output);

      print('✅ Model test successful');
      print('🔍 Test output sample: ${output[0].take(5).toList()}...');
    } catch (e) {
      print('⚠️ Model test failed: $e');
      print('⚠️ Error type: ${e.runtimeType}');
      throw Exception('Model test failed: $e');
    }
  }

  List<double>? runInference(List<List<double>> points) {
    if (!_isModelLoaded || _interpreter == null) {
      print('⚠️ Model not loaded');
      return null;
    }

    try {
      // Use NRDP to normalize input to exactly 100 points
      List<List<double>> processedPoints = nrdp(points, 100);

      // Ensure we have exactly 100 points with 3 dimensions each
      while (processedPoints.length < 100) {
        processedPoints.add([0.0, 0.0, 0.0]);
      }

      // Ensure each point has exactly 3 dimensions (x, y, time)
      for (int i = 0; i < processedPoints.length; i++) {
        while (processedPoints[i].length < 3) {
          processedPoints[i].add(i / 100.0); // Add time index if missing
        }
        if (processedPoints[i].length > 3) {
          processedPoints[i] = processedPoints[i].sublist(0, 3);
        }
      }

      // Create input tensor [1, 100, 3]
      var input = [processedPoints];

      // Create output tensor [1, 11] for 11 classes (0-9 + ♡)
      var output = [List.filled(11, 0.0)];

      // Run inference
      _interpreter!.run(input, output);

      // Return the output probabilities
      return output[0];

    } catch (e) {
      print('❌ Inference error: $e');
      return null;
    }
  }

  void close() {
    try {
      _interpreter?.close();
      _interpreter = null;
      _isModelLoaded = false;
      print('🔒 TFLite model closed');
    } catch (e) {
      print('⚠️ Error closing model: $e');
    }
  }
}

// RDP (Ramer-Douglas-Peucker) algorithm implementation
List<List<double>> rdp(List<List<double>> points, double epsilon) {
  if (points.length <= 2) return points;

  List<double> start = points[0];
  List<double> end = points[points.length - 1];

  double maxDistance = 0;
  int maxIndex = 0;

  for (int i = 1; i < points.length - 1; i++) {
    double distance = perpendicularDistance(points[i], start, end);
    if (distance > maxDistance) {
      maxDistance = distance;
      maxIndex = i;
    }
  }

  if (maxDistance > epsilon) {
    List<List<double>> left = rdp(points.sublist(0, maxIndex + 1), epsilon);
    List<List<double>> right = rdp(points.sublist(maxIndex), epsilon);
    return [...left.sublist(0, left.length - 1), ...right];
  }

  return [start, end];
}

// Calculate perpendicular distance from point to line
double perpendicularDistance(List<double> point, List<double> lineStart, List<double> lineEnd) {
  double x0 = point[0], y0 = point[1];
  double x1 = lineStart[0], y1 = lineStart[1];
  double x2 = lineEnd[0], y2 = lineEnd[1];

  double numerator = ((y2 - y1) * x0 - (x2 - x1) * y0 + x2 * y1 - y2 * x1).abs();
  double denominator = sqrt(pow(y2 - y1, 2) + pow(x2 - x1, 2));

  return denominator == 0 ? 0 : numerator / denominator;
}

// Interpolate points to exact number
List<List<double>> interpolate(List<List<double>> points, int targetCount) {
  if (points.length <= 1) return points;
  if (points.length == targetCount) return points;

  List<List<double>> result = [points[0]];
  double step = (points.length - 1) / (targetCount - 1);

  for (int i = 1; i < targetCount - 1; i++) {
    double index = i * step;
    int lowerIndex = index.floor();
    int upperIndex = index.ceil();
    double t = index - lowerIndex;

    if (lowerIndex == upperIndex) {
      result.add([...points[lowerIndex]]);
    } else {
      List<double> interpolated = [];
      for (int j = 0; j < points[lowerIndex].length; j++) {
        double value = (1 - t) * points[lowerIndex][j] + t * points[upperIndex][j];
        interpolated.add(value);
      }
      result.add(interpolated);
    }
  }

  result.add([...points[points.length - 1]]);
  return result;
}

// Normalize array to [0,1] range
List<List<double>> normalizeArray(List<List<double>> points) {
  if (points.isEmpty) return points;

  double minX = double.infinity, maxX = double.negativeInfinity;
  double minY = double.infinity, maxY = double.negativeInfinity;

  for (var point in points) {
    if (point.length >= 2) {
      if (point[0] < minX) minX = point[0];
      if (point[0] > maxX) maxX = point[0];
      if (point[1] < minY) minY = point[1];
      if (point[1] > maxY) maxY = point[1];
    }
  }

  return points.map((point) {
    if (point.length >= 2) {
      double normalizedX = maxX != minX ? (point[0] - minX) / (maxX - minX) : 0.0;
      double normalizedY = maxY != minY ? (point[1] - minY) / (maxY - minY) : 0.0;
      return [normalizedX, normalizedY, ...point.sublist(2)];
    }
    return [...point];
  }).toList();
}

// Adjust epsilon to get target number of points
Map<String, dynamic> adjustEpsilon(List<List<double>> points, int targetCount) {
  double minEpsilon = 0.0;
  double maxEpsilon = 0.0;

  // Find max distance to set initial range
  for (int i = 1; i < points.length - 1; i++) {
    double distance = perpendicularDistance(points[i], points[0], points[points.length - 1]);
    if (distance > maxEpsilon) maxEpsilon = distance;
  }

  double epsilon = (minEpsilon + maxEpsilon) / 2;
  List<List<double>> result = [];

  while (minEpsilon <= maxEpsilon) {
    result = rdp(points, epsilon);

    if (result.length == targetCount) break;

    if (result.length > targetCount) {
      minEpsilon = epsilon + double.minPositive;
    } else {
      maxEpsilon = epsilon - double.minPositive;
    }

    epsilon = (minEpsilon + maxEpsilon) / 2;

    if ((maxEpsilon - minEpsilon).abs() < double.minPositive) break;
  }

  return {'epsilon': epsilon, 'points': result};
}

// Main NRDP function - Normalized RDP with target count
List<List<double>> nrdp(List<List<double>> points, [int targetCount = 100]) {
  if (points.isEmpty) return [];

  List<List<double>> result;

  if (points.length > targetCount) {
    Map<String, dynamic> adjusted = adjustEpsilon(points, targetCount);
    result = adjusted['points'];

    if (result.length != targetCount) {
      result = interpolate(result, targetCount);
    }
  } else {
    result = interpolate(points, targetCount);
  }

  return normalizeArray(result);
}
