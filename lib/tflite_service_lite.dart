import 'package:flutter/services.dart';
import 'package:tflite_flutter/tflite_flutter.dart';
import 'dart:typed_data';
import 'dart:math';

class TFLiteServiceLite {
  Interpreter? _interpreter;
  bool _isModelLoaded = false;

  bool get isModelLoaded => _isModelLoaded;

  Future<bool> loadModel() async {
    try {
      print('🤖 Loading TFLite model...');

      // Check if TensorFlow Lite is available
      try {
        // Try to create a simple interpreter first to test if TFLite works
        print('🔍 Testing TensorFlow Lite availability...');

        // Check if model file exists
        print('📂 Attempting to load model from assets...');
        final ByteData modelBytes = await rootBundle.load('assets/models/gesture_model1.tflite');
        print('📁 Model file loaded successfully, size: ${modelBytes.lengthInBytes} bytes');

        if (modelBytes.lengthInBytes == 0) {
          throw Exception('Model file is empty');
        }

        final Uint8List modelData = modelBytes.buffer.asUint8List();
        print('🔄 Model data converted to Uint8List, length: ${modelData.length}');

        // Create interpreter with multiple fallback strategies
        print('🔧 Creating TFLite interpreter...');
        Exception? lastError;

        // Strategy 1: Default options
        try {
          print('🔧 Trying default interpreter options...');
          _interpreter = Interpreter.fromBuffer(modelData);
          print('✅ Interpreter created successfully with default options');
        } catch (e1) {
          lastError = e1 is Exception ? e1 : Exception(e1.toString());
          print('⚠️ Default interpreter failed: $e1');

          // Strategy 2: Custom options with minimal configuration
          try {
            print('🔧 Trying custom interpreter options...');
            final options = InterpreterOptions()
              ..threads = 1;
            _interpreter = Interpreter.fromBuffer(modelData, options: options);
            print('✅ Interpreter created successfully with custom options');
          } catch (e2) {
            lastError = e2 is Exception ? e2 : Exception(e2.toString());
            print('⚠️ Custom interpreter failed: $e2');

            // Strategy 3: Minimal options with different thread count
            try {
              print('🔧 Trying minimal interpreter options...');
              final options = InterpreterOptions()..threads = 2;
              _interpreter = Interpreter.fromBuffer(modelData, options: options);
              print('✅ Interpreter created successfully with minimal options');
            } catch (e3) {
              lastError = e3 is Exception ? e3 : Exception(e3.toString());
              print('❌ All interpreter creation strategies failed');
              throw lastError;
            }
          }
        }

        // Get model info if interpreter was created successfully
        if (_interpreter != null) {
          try {
            final inputTensors = _interpreter!.getInputTensors();
            final outputTensors = _interpreter!.getOutputTensors();
            print('📊 Input tensors: ${inputTensors.length}');
            print('📊 Output tensors: ${outputTensors.length}');

            if (inputTensors.isNotEmpty) {
              print('📊 Input shape: ${inputTensors[0].shape}');
              print('📊 Input type: ${inputTensors[0].type}');
            }

            if (outputTensors.isNotEmpty) {
              print('📊 Output shape: ${outputTensors[0].shape}');
              print('📊 Output type: ${outputTensors[0].type}');
            }
          } catch (infoError) {
            print('⚠️ Could not get model info: $infoError');
          }
        }

        // Mark as loaded first, then test
        _isModelLoaded = true;
        print('✅ TFLite model loaded successfully');

        // Test the model with dummy data to ensure it works
        print('🧪 Testing model with dummy data...');
        try {
          await _testModel();
          print('✅ Model test passed - TensorFlow Lite is fully functional');
        } catch (testError) {
          print('⚠️ Model test failed but interpreter was created: $testError');
          print('💡 Model may still work with real data');
          // Keep model loaded even if test fails - might work with real data
        }

        return true;

      } catch (assetError) {
        print('❌ Error loading model asset: $assetError');
        throw assetError;
      }

    } catch (e) {
      print('❌ Error loading TFLite model: $e');
      print('❌ Error type: ${e.runtimeType}');
      _isModelLoaded = false;

      // Provide specific error information and suggestions
      String errorStr = e.toString().toLowerCase();
      if (errorStr.contains('libtensorflowlite_c.so') || errorStr.contains('dynamic library')) {
        print('💡 TensorFlow Lite native library issue detected.');
        print('🔧 Possible solutions:');
        print('   1. Update tflite_flutter plugin to latest version');
        print('   2. Add NDK configuration in android/app/build.gradle');
        print('   3. Clean and rebuild the project');
        print('   4. Check if device architecture is supported');
        print('📱 Falling back to simulation mode for now');
      } else if (errorStr.contains('tflite') || errorStr.contains('interpreter')) {
        print('💡 TensorFlow Lite interpreter issue detected.');
        print('🔧 This might be due to:');
        print('   - Incompatible model format');
        print('   - Plugin initialization failure');
        print('   - Device compatibility issues');
      }

      return false;
    }
  }

  Future<void> _testModel() async {
    if (_interpreter == null) return;

    try {
      // Create dummy input data for testing [1, 100, 3]
      var input = List.generate(1, (i) => List.generate(100, (j) => List.generate(3, (k) => 0.5)));
      var output = List.generate(1, (i) => List.generate(11, (j) => 0.0));

      print('🧪 Input shape: [${input.length}, ${input[0].length}, ${input[0][0].length}]');
      print('🧪 Output shape: [${output.length}, ${output[0].length}]');

      // Run a test inference
      _interpreter!.run(input, output);

      print('✅ Model test successful');
      print('🔍 Test output sample: ${output[0].take(5).toList()}...');
    } catch (e) {
      print('⚠️ Model test failed: $e');
      print('⚠️ Error type: ${e.runtimeType}');
      throw Exception('Model test failed: $e');
    }
  }

  List<double>? runInference(List<List<double>> points) {
    if (!_isModelLoaded || _interpreter == null) {
      print('⚠️ Model not loaded');
      return null;
    }

    try {
      // Use NRDP to normalize input to exactly 100 points
      List<List<double>> processedPoints = nrdp(points, 100);

      // Ensure we have exactly 100 points with 3 dimensions each
      while (processedPoints.length < 100) {
        processedPoints.add([0.0, 0.0, 0.0]);
      }

      // Ensure each point has exactly 3 dimensions (x, y, time)
      for (int i = 0; i < processedPoints.length; i++) {
        while (processedPoints[i].length < 3) {
          processedPoints[i].add(i / 100.0); // Add time index if missing
        }
        if (processedPoints[i].length > 3) {
          processedPoints[i] = processedPoints[i].sublist(0, 3);
        }
      }

      // Create input tensor [1, 100, 3]
      var input = [processedPoints];

      // Create output tensor [1, 11] for 11 classes (0-9 + ♡)
      var output = [List.filled(11, 0.0)];

      // Run inference
      _interpreter!.run(input, output);

      // Return the output probabilities
      return output[0];

    } catch (e) {
      print('❌ Inference error: $e');
      return null;
    }
  }

  void close() {
    try {
      _interpreter?.close();
      _interpreter = null;
      _isModelLoaded = false;
      print('🔒 TFLite model closed');
    } catch (e) {
      print('⚠️ Error closing model: $e');
    }
  }
}

// RDP (Ramer-Douglas-Peucker) algorithm implementation
List<List<double>> rdp(List<List<double>> points, double epsilon) {
  if (points.length <= 2) return points;

  List<double> start = points[0];
  List<double> end = points[points.length - 1];

  double maxDistance = 0;
  int maxIndex = 0;

  for (int i = 1; i < points.length - 1; i++) {
    double distance = perpendicularDistance(points[i], start, end);
    if (distance > maxDistance) {
      maxDistance = distance;
      maxIndex = i;
    }
  }

  if (maxDistance > epsilon) {
    List<List<double>> left = rdp(points.sublist(0, maxIndex + 1), epsilon);
    List<List<double>> right = rdp(points.sublist(maxIndex), epsilon);
    return [...left.sublist(0, left.length - 1), ...right];
  }

  return [start, end];
}

// Calculate perpendicular distance from point to line
double perpendicularDistance(List<double> point, List<double> lineStart, List<double> lineEnd) {
  double x0 = point[0], y0 = point[1];
  double x1 = lineStart[0], y1 = lineStart[1];
  double x2 = lineEnd[0], y2 = lineEnd[1];

  double numerator = ((y2 - y1) * x0 - (x2 - x1) * y0 + x2 * y1 - y2 * x1).abs();
  double denominator = sqrt(pow(y2 - y1, 2) + pow(x2 - x1, 2));

  return denominator == 0 ? 0 : numerator / denominator;
}

// Interpolate points to exact number
List<List<double>> interpolate(List<List<double>> points, int targetCount) {
  if (points.length <= 1) return points;
  if (points.length == targetCount) return points;

  List<List<double>> result = [points[0]];
  double step = (points.length - 1) / (targetCount - 1);

  for (int i = 1; i < targetCount - 1; i++) {
    double index = i * step;
    int lowerIndex = index.floor();
    int upperIndex = index.ceil();
    double t = index - lowerIndex;

    if (lowerIndex == upperIndex) {
      result.add([...points[lowerIndex]]);
    } else {
      List<double> interpolated = [];
      for (int j = 0; j < points[lowerIndex].length; j++) {
        double value = (1 - t) * points[lowerIndex][j] + t * points[upperIndex][j];
        interpolated.add(value);
      }
      result.add(interpolated);
    }
  }

  result.add([...points[points.length - 1]]);
  return result;
}

// Normalize array to [0,1] range
List<List<double>> normalizeArray(List<List<double>> points) {
  if (points.isEmpty) return points;

  double minX = double.infinity, maxX = double.negativeInfinity;
  double minY = double.infinity, maxY = double.negativeInfinity;

  for (var point in points) {
    if (point.length >= 2) {
      if (point[0] < minX) minX = point[0];
      if (point[0] > maxX) maxX = point[0];
      if (point[1] < minY) minY = point[1];
      if (point[1] > maxY) maxY = point[1];
    }
  }

  return points.map((point) {
    if (point.length >= 2) {
      double normalizedX = maxX != minX ? (point[0] - minX) / (maxX - minX) : 0.0;
      double normalizedY = maxY != minY ? (point[1] - minY) / (maxY - minY) : 0.0;
      return [normalizedX, normalizedY, ...point.sublist(2)];
    }
    return [...point];
  }).toList();
}

// Adjust epsilon to get target number of points
Map<String, dynamic> adjustEpsilon(List<List<double>> points, int targetCount) {
  double minEpsilon = 0.0;
  double maxEpsilon = 0.0;

  // Find max distance to set initial range
  for (int i = 1; i < points.length - 1; i++) {
    double distance = perpendicularDistance(points[i], points[0], points[points.length - 1]);
    if (distance > maxEpsilon) maxEpsilon = distance;
  }

  double epsilon = (minEpsilon + maxEpsilon) / 2;
  List<List<double>> result = [];

  while (minEpsilon <= maxEpsilon) {
    result = rdp(points, epsilon);

    if (result.length == targetCount) break;

    if (result.length > targetCount) {
      minEpsilon = epsilon + double.minPositive;
    } else {
      maxEpsilon = epsilon - double.minPositive;
    }

    epsilon = (minEpsilon + maxEpsilon) / 2;

    if ((maxEpsilon - minEpsilon).abs() < double.minPositive) break;
  }

  return {'epsilon': epsilon, 'points': result};
}

// Main NRDP function - Normalized RDP with target count
List<List<double>> nrdp(List<List<double>> points, [int targetCount = 100]) {
  if (points.isEmpty) return [];

  List<List<double>> result;

  if (points.length > targetCount) {
    Map<String, dynamic> adjusted = adjustEpsilon(points, targetCount);
    result = adjusted['points'];

    if (result.length != targetCount) {
      result = interpolate(result, targetCount);
    }
  } else {
    result = interpolate(points, targetCount);
  }

  return normalizeArray(result);
}
