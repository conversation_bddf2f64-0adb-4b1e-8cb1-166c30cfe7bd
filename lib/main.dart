import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'tflite_service_lite.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final cameras = await availableCameras();
  runApp(DrawingApp(cameras: cameras));
}

class DrawingApp extends StatelessWidget {
  final List<CameraDescription> cameras;
  
  const DrawingApp({super.key, required this.cameras});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Multi-Tab App',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: MainTabView(cameras: cameras),
    );
  }
}

class MainTabView extends StatefulWidget {
  final List<CameraDescription> cameras;
  
  const MainTabView({super.key, required this.cameras});

  @override
  State<MainTabView> createState() => _MainTabViewState();
}

class _MainTabViewState extends State<MainTabView> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('Multi-Tab App'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.draw), text: 'Draw'),
            Tab(icon: Icon(Icons.camera_alt), text: 'Camera'),
            Tab(icon: Icon(Icons.waving_hand), text: 'Hello'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        physics: const NeverScrollableScrollPhysics(), // Disable swipe navigation completely
        children: [
          const DrawingCanvas(),
          CameraView(cameras: widget.cameras),
          const HelloView(),
        ],
      ),
    );
  }
}

class DrawingCanvas extends StatefulWidget {
  const DrawingCanvas({super.key});

  @override
  State<DrawingCanvas> createState() => _DrawingCanvasState();
}

class _DrawingCanvasState extends State<DrawingCanvas> {
  List<Offset?> points = <Offset?>[];
  final TFLiteServiceLite _tfliteService = TFLiteServiceLite();
  bool _modelLoaded = false;
  String _predValue = '';
  Size? _canvasSize;

  @override
  void initState() {
    super.initState();
    _loadTFLite();
  }

  @override
  void dispose() {
    _tfliteService.close();
    super.dispose();
  }

  Future<void> _loadTFLite() async {
    try {
      bool success = await _tfliteService.loadModel();
      setState(() {
        _modelLoaded = success;
      });
    } catch (e) {
      print('Failed to load TFLite model: $e');
      setState(() {
        _modelLoaded = false;
      });
    }
  }

  void _runGestureRecognition() {
    if (!_modelLoaded || points.isEmpty) {
      _simulateGestureRecognition();
      return;
    }

    try {
      // Filter out null points
      List<Offset> validPoints = points.where((p) => p != null).cast<Offset>().toList();

      if (validPoints.isEmpty) return;

      // Convert points to normalized coordinates with time index
      List<List<double>> normalizedPoints = [];
      for (int i = 0; i < validPoints.length; i++) {
        final point = validPoints[i];
        double normalizedX = _canvasSize != null ? point.dx / _canvasSize!.width : point.dx / 300.0;
        double normalizedY = _canvasSize != null ? point.dy / _canvasSize!.height : point.dy / 300.0;
        double timeIndex = i / 100.0;

        normalizedPoints.add([normalizedX, normalizedY, timeIndex]);
      }

      // Run inference
      List<double>? rawOutput = _tfliteService.runInference(normalizedPoints);

      if (rawOutput != null) {
        // Find the index with highest score
        int bestIndex = 0;
        for (int i = 1; i < rawOutput.length; i++) {
          if (rawOutput[i] > rawOutput[bestIndex]) {
            bestIndex = i;
          }
        }

        // Map index to label
        List<String> labels = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '♡'];
        if (bestIndex < labels.length) {
          String predictedChar = labels[bestIndex];

          setState(() {
            _predValue += predictedChar;
            if (_predValue.length > 8) {
              _predValue = _predValue.substring(_predValue.length - 8);
            }
          });
        }
      } else {
        _simulateGestureRecognition();
      }
    } catch (e) {
      print('Error during gesture recognition: $e');
      _simulateGestureRecognition();
    }
  }

  void _simulateGestureRecognition() {
    // Fallback simulation
    List<String> labels = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '♡'];
    String randomPred = labels[(DateTime.now().millisecondsSinceEpoch % labels.length)];

    setState(() {
      _predValue += randomPred;
      if (_predValue.length > 8) {
        _predValue = _predValue.substring(_predValue.length - 8);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: const Text(
            '🎨 Touch to draw! Touch down clears and starts drawing.',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          child: Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey, width: 2),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onPanStart: (details) {
                setState(() {
                  points.clear();
                  points.add(details.localPosition);
                });
              },
              onPanUpdate: (details) {
                setState(() {
                  points.add(details.localPosition);
                });
              },
              onPanEnd: (details) {
                // Run gesture recognition when drawing ends
                _runGestureRecognition();
              },
              child: LayoutBuilder(
                builder: (context, constraints) {
                  _canvasSize = Size(constraints.maxWidth, constraints.maxHeight);
                  return CustomPaint(
                    painter: DrawingPainter(points),
                    size: Size.infinite,
                  );
                },
              ),
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        points.clear();
                      });
                    },
                    icon: const Icon(Icons.clear),
                    label: const Text('Clear'),
                  ),
                  Text(
                    'Points: ${points.length}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  children: [
                    Text(
                      'Gesture Recognition',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _modelLoaded ? Icons.smart_toy : Icons.psychology,
                          color: _modelLoaded ? Colors.green : Colors.orange,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _modelLoaded ? 'AI Model Ready' : 'Simulation Mode',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Predictions: ${_predValue.isEmpty ? "Draw something!" : _predValue}',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontFamily: 'monospace',
                        fontWeight: FontWeight.bold,
                        color: Colors.deepPurple,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class DrawingPainter extends CustomPainter {
  final List<Offset?> points;

  DrawingPainter(this.points);

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = Colors.deepPurple
      ..strokeCap = StrokeCap.round
      ..strokeWidth = 3.0;

    for (int i = 0; i < points.length - 1; i++) {
      if (points[i] != null && points[i + 1] != null) {
        canvas.drawLine(points[i]!, points[i + 1]!, paint);
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

class CameraView extends StatefulWidget {
  final List<CameraDescription> cameras;
  
  const CameraView({super.key, required this.cameras});

  @override
  State<CameraView> createState() => _CameraViewState();
}

class _CameraViewState extends State<CameraView> {
  CameraController? _controller;
  Future<void>? _initializeControllerFuture;

  @override
  void initState() {
    super.initState();
    if (widget.cameras.isNotEmpty) {
      _controller = CameraController(
        widget.cameras.first,
        ResolutionPreset.medium,
      );
      _initializeControllerFuture = _controller!.initialize();
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.cameras.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: const [
            Icon(Icons.camera_alt_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No cameras available',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return FutureBuilder<void>(
      future: _initializeControllerFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return Column(
            children: [
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey, width: 2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(6),
                    child: CameraPreview(_controller!),
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () async {
                        try {
                          await _initializeControllerFuture;
                          final image = await _controller!.takePicture();
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Picture saved: ${image.path}'),
                              ),
                            );
                          }
                        } catch (e) {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('Error: $e')),
                            );
                          }
                        }
                      },
                      icon: const Icon(Icons.camera),
                      label: const Text('Take Photo'),
                    ),
                  ],
                ),
              ),
            ],
          );
        } else {
          return const Center(child: CircularProgressIndicator());
        }
      },
    );
  }
}

class HelloView extends StatelessWidget {
  const HelloView({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            '👋',
            style: TextStyle(fontSize: 100),
          ),
          const SizedBox(height: 20),
          const Text(
            'HI!',
            style: TextStyle(
              fontSize: 48,
              fontWeight: FontWeight.bold,
              color: Colors.deepPurple,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Welcome to the Hello tab!',
            style: TextStyle(fontSize: 18),
          ),
          const SizedBox(height: 40),
          ElevatedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Hello there! 👋'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            icon: const Icon(Icons.waving_hand),
            label: const Text('Say Hello'),
          ),
        ],
      ),
    );
  }
}
