{"version": 2, "files": [{"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "d7393d6e408c5f8cf5af1efbf79fe17b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "479493da08b4e2137fc162ff23bef99b"}, {"path": "/sdks/flutter/packages/flutter/lib/rendering.dart", "hash": "6dbd011ca31387e3192d39c999a1dd9f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "b41739c7771e51de19e76067f9a2671f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "e0434f358d6cfe80754357597dda5e2d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "2eb2de173e53bab6a0a033f89856eae6"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "d17e0458ef2b4cdffbc13c8b6b441a9e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "52de52753447696c8bf45bf27bc1a8e6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "61c96752b1e816d7add82a5c9881a31f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "d5a62fdcbdcfbb765af4b2c4c30adb47"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "a4452128d10ae55b5e2c701edda87207"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/events/camera_event.dart", "hash": "c51bc3a7c3a644c817818bb129a1a2ae"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "34b8b9a525c2edf0191fbd9f925005c8"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "fd2a12edfd4b98e55a3b84dc04d027b2"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "63d58416836c36cc1685e1b404d4a1e8"}, {"path": "/sdks/flutter/packages/flutter/lib/services.dart", "hash": "b74bf26ae8105a75eb390fed5aaf29cd"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "ea7b5731a74e12ea2a31a21b8c188444"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "319c25a79e3bd23b58ab78b5a0aa0d34"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "5acfa8abbd3e0d70f514ac65e7da5061"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "c5e481f1a4ef788a50f4aa68fb4c0d7c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "51aacf05a9f117da54a963931538b538"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "3b50c5a9642520832ec2c51c8efef598"}, {"path": "/app/.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/x86_64/app.so", "hash": "48f512628366597646f82f323b9ff0ea"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "9eb9cc50cf100f8006d01f4027d2f5ec"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "ab7f9e1c4cf503e32f4c64defec28a06"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/delegates/xnnpack_delegate.dart", "hash": "fbac5b2b83403d0fda7531e28abe311b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "457092647c06b9d19ea071d0ef4fba09"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "46a3d1bc9290ef51b685700434b32b44"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "58fb19f1ca230f5249014a6ea4283e1a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "c9d67cc655fb9b02d52b68e9f039bc41"}, {"path": "/sdks/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "91d3fd03ed8299fe37d1486bd87ae6e7"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7/lib/src/type_conversion.dart", "hash": "d85a8df814eee3799472722f2c419a19"}, {"path": "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "a1be5291e9ce315ce3e7ad6598d6edb7"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/app/build/app/intermediates/flutter/release/x86_64/app.so", "hash": "48f512628366597646f82f323b9ff0ea"}, {"path": "/app/build/app/intermediates/flutter/release/flutter_assets/shaders/ink_sparkle.frag", "hash": "7f2fb21c467e6cfa00b4c27f4c784476"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "a3068ae1637db004de0bcac7634f398e"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/root/.pub-cache/hosted/pub.dev/cross_file-0.3.3+6/lib/cross_file.dart", "hash": "b5c8f4dba868efb80ed69fcd5a7d3f07"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "1a773319137363e711d5189770d4517c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "e3fcfca0756d2591ba7a72d8cb8073b9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "124e39caf0044d319a0a82f95cce01d9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "0711824f5f77fc221ca16dd2833c1db4"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "8559baab97364a51be134a959392660a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "3344aee98c44f74ea6fb76c2726dfded"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/tap.dart", "hash": "2174cee3aa85b6a1cb77f1e9f1f54f7b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "55d87de41cd8f095ff29b79280667bce"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "6e800790e7858e8e1cdc73c8cc09d719"}, {"path": "/root/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.6/lib/plugin_platform_interface.dart", "hash": "510b78d72d59a887b1ccc3deb30c44c1"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera-0.10.5+5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "f89e43c1dd5f54c745d4a22e7f629e62"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/delegates/metal_delegate.dart", "hash": "26e2fa2666f2489d69278e1fb982d5f2"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/quantizer_wsmeans.dart", "hash": "c98d039d1ce1e92fa79f1039f0263385"}, {"path": "/root/.pub-cache/hosted/pub.dev/fake_async-1.3.1/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "6c7a86378419565d1822850ce87fcb02"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "d4d8de3042f1567cf3c3e4c69035e122"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/feedback.dart", "hash": "b8cec7a5e5d9ccffefa0e7539e92e49f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "25fc1338a5818b8b96655ddd6b3d5fa3"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "9d633da4b6e0a89f1c7cd8451b8fb9f5"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "1e4b9425e7c56b52cd233c8b17fcce78"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "83dde9913aa4a689b698d2ba5ec7c676"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "088b09d02a0f2cafcd87b0fe5f66c83e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "f3ed1f2bbf9255947413e97c37fc8170"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "645e6e5a42aafa161b24b84bd41fb0ed"}, {"path": "/app/assets/models/gesture_model1.tflite", "hash": "8f5263f2727effe133bfb68bbad43c3c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "d0da5ea7955f48054ae624e16528193f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "e71d390cbb4f63b91608b6ae8a37321c"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "8eb7f9d0796943d7edb0bc187df830f0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/flutter_logo.dart", "hash": "3c4879149bc0b27f216987d9377b87a6"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "6a2092bc6e531cf53fd4151ac189cfd4"}, {"path": "/app/.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/armeabi-v7a/app.so", "hash": "1d036d663e2f4e86c273f07ed0440a22"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "2e0e0e4e8c040ded0b0a7a22afba6066"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "051453bb7d9c8983ad2fb529b4ef462f"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/interpreter.dart", "hash": "4d8b581ab62858eaa4b39b6c41eda974"}, {"path": "/sdks/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/math.dart", "hash": "aa4c24c010fb444c843ce3dc261a569f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "d20e1a7a4860303669f49b2fcf70cc1b"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/lib/src/android_camera.dart", "hash": "8211faf1ed46873819c4ebb433f45945"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "6a7cef816d662014b8fff82eafd4bc39"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "f63bc535d25a8bcf67f0acc5d71c9252"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "ff49202dfcd82a646d59d2012330173a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "40fa279b08807ed54fc6415e40e74890"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "9d8a7927ca14b15f3473e211789eaa52"}, {"path": "/root/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.6/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/sdks/flutter/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "9936f30321ebd2d53045f2c2bb4f91f0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "ad8955cdfbda21f8ebbc146a61748940"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "cff85360d3ed608c6c25e665f1d62d82"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/camera_description.dart", "hash": "c1303099b7d9098e69303ddd2d09d4b3"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "0981d0ab711c3d970bf986b59e302ec3"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/util/list_shape_extension.dart", "hash": "f795987424e22e751565c9f84d8faf45"}, {"path": "/sdks/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "e54cc4c43086300a6e6c976bfcc4b577"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "665aca3a93721bc97cb6aeaae166ce98"}, {"path": "/sdks/flutter/bin/internal/engine.version", "hash": "61910d260e056a54ce01941dccf781ca"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "6aee7a8b65e3407c8a156fc94c3e052b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "1b3e0df1cc1fdb0ff927760c5c7dceda"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "ca775fd1da8b4c902a08d53153ec4ab1"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "687bbf9e1a7bddd4b55174fb07cb6919"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "f6da0daf266187d3ab6ea8ae16772aec"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "6570ec9fa980d0f831f98aff35b3a1ac"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "e4868d928bcb5b32093caa9f9720c7cb"}, {"path": "/root/.pub-cache/hosted/pub.dev/cross_file-0.3.3+6/lib/src/types/io.dart", "hash": "71a2eb35aecd8b5de95a7c8c38f276d2"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "334d5b02e9aa5fc27e87fdf1a49140cd"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "b324a1cbdb9418b4698cfec22372af7d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "b57fb186ac13e07cabcf79e9203ce0f8"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "9ef38c4477b39a88b44a7c9bb85add99"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/union_set_controller.dart", "hash": "fcfae3ecf984ee7d09081c2a7898dcab"}, {"path": "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/src/grapheme_clusters/table.dart", "hash": "126c63b07d1b425e904b735cdac85afd"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "7d4ac5744bd9691f24fc3fc585a139aa"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "2b77873b8c720bf68e043ce7acd96985"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/aggregate_sample.dart", "hash": "b966e599b668b271e40d13fb47cc0e5d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "9cbb91c1b2e44cbd834663485bf49156"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "a4a5a4186c28c8e4e7ef38558ae2fa14"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "a6f0f004f94c7166d2961fba341637c4"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "e3104f80561c2766e0404a2e86c1eb4b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "6d12493069f2abe32b2c0ce97b36f663"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "0f4cb9768fb17bf7a5d45753b15a2092"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "74ffe935dac1b387e33d85915c6bca90"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/lib/src/utils.dart", "hash": "279ab400656a09dd3ac7ce8b38c5c4a6"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/from_handlers.dart", "hash": "7b90ec0037931c3884c97e01429c2d93"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/combined_wrappers/combined_list.dart", "hash": "81b2d2a545e6f66510367ee8d4bdbf51"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "aea224eef2ce1d7e59c255050e32538b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "c6fe70279c1fabcc739026bb9cfb2eb8"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "9a213a6ad0048a7638eb152b56018b0b"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/hct/cam16.dart", "hash": "fbc5d5b64be654228c88182f2f0e9f1e"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/merge.dart", "hash": "8faa403505c3d8ec3ce7f225b6e46526"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "df941eca11a421d04edd92726e3a4321"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "d9bab5fd98c4aeb46f0ee46466d27592"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "06ab11848273dcdc3ea237593b87fe98"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "3eb370b59dae381bbca5781ca5f11add"}, {"path": "/sdks/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "f75f31535e16b018e2a5f9a968b7254c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "ce1cedd9270d5130bb4be660d13d846e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "7725ee45d15fa56992b64ca1060bcfef"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "3d26622e062df504e7d6d5ecf06c1d32"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "ec004619fab55892c218a0adf229b561"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "d6f1398a30e5929b4deccbeb0321c7df"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "433b37dcc0e33393a88d42baea923fa2"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "33f365e8e51713d9712771b0adcbbca3"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/list_extensions.dart", "hash": "5e568872f7db50efc3ab16b3deb3d72c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "2e7c0078255c745b56a2bf1bf7992df3"}, {"path": "/sdks/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "1af4ec1307aa8270fb0739708f0c0882"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "03372d734d84040c509f3cacf8315edf"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "9e3b68924814d1e0d7f46f7ce20fa741"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "792cf5ae893012a7a57a7de753af1110"}, {"path": "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/src/characters.dart", "hash": "21bf6725b1fc374f03ae5b2cb46bd95b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "c4f3f94bbecf67e4419ca6a8e97d8715"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "46fb99dbb9a3eb279a14292c7a2ab6f6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "f901250cbd5c13750f08f7888069e632"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "4a64d3243f3e64ba6289d9c05d2d8352"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "4449a9ff2f8ef7121f4e0e47a6cf4bb6"}, {"path": "/app/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "a5ac2c6aef4b178f856c2e4afa30a030"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/combined_wrappers/combined_iterable.dart", "hash": "73a1264f4048161cf1e020b447b014a8"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "38e9bbf0c4b9be4d755c28884b84b322"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "4bc0791cae09df89eaa7f2873b01d70a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "51a93044fe489d37f82f368179e3d302"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "2accc934e05b459de8fe3362f452102f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "77fcee8d4672891b204152fd54ec8143"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "0eef32ab9b2cf423c48e89f2dcd9bd6b"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/stream_transform.dart", "hash": "2f811178fd6401d1399cf8b09cc1f9f4"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "a39ccba3f049cbdd03baf58f3ada518f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "7ad4b1f7d7435272c14803d189af6d13"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "11ddb14d335c7abe7d01544d689a9cfc"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "6bc029efa11f69cb20f3abf8436e9088"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "bc8cb2656c892c7107ece225b2c3ad4d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "2e8b626fa3b55a9701a9e465056b3c34"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "9886ca25d16f34e46171e428c5014d21"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "038416fd6da9501daae9bc9552db8384"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "c5ecbaa65517267fe86dc298ec449627"}, {"path": "/root/.pub-cache/hosted/pub.dev/lints-2.0.1/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "8fa30dbbbc4cdd0646a81c9f5cea46f0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "ea5416329e2b15ccc3c6ba2082e5b17d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "309fca388c4496bad5fbfe0dc5f79fdf"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/lib/camera_android.dart", "hash": "c7aee55d4bc520e0d56f6043eb7a48c6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "331387ea77fae9eee85b67f5e31abf6c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "4d69882445eaa1de4c2e1555c9a08a9d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "4ae7cf672458e9048b8349c46c96091b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "68990009a2ffd358f279c0941f5acb15"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "f5679fafea18b2907b3995e2f751f15f"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "690a90a4ee81c904e39ecef223720fe5"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "04dd39d6f80a5425d8df2875f2833711"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "43087afd2f07c2185dfff2ea8c40cbed"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera-0.10.5+5/lib/camera.dart", "hash": "f456b1774ca0bda3fa7a214381280d46"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "58a520f0e39b9e2df9528496ad1a2c9c"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "6b92d8f12a7fb46649297e25d2cf2b34"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "5a938eb8e6d0513d8448fe7adf46ffd6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "f09d6224689342ce8e364e6ba5fea1c7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "2c822e790480edf5d56f0b6001bb05ed"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "101afb5724d465fef432403afe4af239"}, {"path": "/sdks/flutter/packages/flutter/lib/semantics.dart", "hash": "dfcc453f5331ec6f2c70cda394065785"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "aa5e38a67607d3ac92328300d453f203"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "ed6bc3c2d3f1ffcbdcea972d430440eb"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/hct/hct.dart", "hash": "88e82a2807d91088b55f29e00aa780d9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "959489b18fda284c434701586b43c66b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "8519987f955d72de5736f71fe1b3b96d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "1eb9c499979adc0aa7a4c188cb354fa7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "758d8cac59a8861109cb1c0c455130b7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "61309d04374f7afb4ec42e6bdc24e920"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/app/build/app/intermediates/flutter/release/arm64-v8a/app.so", "hash": "8f90e51c5ee3a795c856ce6db32e3f48"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "d4f2c32d896c5cb74142f43f86b908ea"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "c3eb7baf4000b511ca5b10c22dbb3314"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "d5da49989b2fec96c7bf048f237909ae"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "4b7fdaf8eb582d24d5ce30f8ccfeb47d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "63301a1f08f8a8b9808b22662f4c1bba"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "c6a45f573f549f569f65331bf54ec4b9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "f5af2a566953c579de1c0a3522632033"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "d00bb38035b66ea4837b0e3836742215"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "3c72e68db8be4e65cb349ff74568eb53"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "e8ef425330dd4e0f06b8cdb9762f3d38"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "bd34896b1432d6f707498d3df7a7c3ae"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/async_expand.dart", "hash": "93ada54626ceaa8319d9589347af9762"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "e0c22343cfc4e2772a491e04ba0d7fb0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "ea66c182859f2263bd2be879287d7325"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "a3cb494a3157a15b4bc6e112bd457b2f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "e8149b1e154e1576100fdb79402a824b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "892528317c4253ae78167230f7d4beba"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/utils/utils.dart", "hash": "8631af0b609ed028cbfbccbb4b2232a4"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "c5e89efad0dec2ca225efaba106f1009"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "b6f7217e92bd0cd6308e102b91a73ee7"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/boollist.dart", "hash": "b3d51ec0dc553b1b1e883e087d5c73cd"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "4a01954c6cb77f1d4f678b32ae946189"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "6648a14df2e64bca06988745d17f14e1"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "7ce4485fb80ddad6acd68abaf480ecec"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "8a3ebe88566064230a8c53e90fb9a5d4"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "1f2f230ab658e9a20934e8821367f21d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "ab35fb35a2d6cac70b40210ff2304fd0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "d755fc45af3dc64d82c199592ba93faf"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "1e3139880d4a71af922ea4538ed3a693"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "f5c94ee1d5dde386af79c59872fd148d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "24c1035fad541d49bd16d2b2bdd194b5"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "3f80d418ed7e7aec7e3dac422ccc4fda"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "9e01d9cd61659f3d0ec6a98a92293d76"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/about.dart", "hash": "f70e9fcb5bea790c73d51431ae8e976f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "7dae504d7c9e221fb01d1901a657407b"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/functions.dart", "hash": "a3aa36a805436731699f39e6bf524087"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "bd5ab55eaa960294d369e4aa6d62ec01"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "c58890a97d5cf176bc4d91c6833662d3"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "88ea97c9aa108e7dfd5d70c21aa809ac"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "3f6fe76fd1b6ce55578b873386c13ff7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "d97a506e94a9f098ecdb1483ca9707af"}, {"path": "/sdks/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "8ff79ac46e7b7c3daaab8484ef94370f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "f483e18d0caffec1965cc4fc4587bced"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "d98eaef69fe0b763e12fde9bb4b63a8a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "8051a4ac9b61f1304162308056f35ca9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "418e1664a31b32fc7ee9b7c90f31a67c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/material.dart", "hash": "27ba12c7559ee577cf56315e3d5cb5a3"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera-0.10.5+5/lib/src/camera_preview.dart", "hash": "90d4b1e3049bf1cf0d082e951f032809"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "faa0ac8a10d4bbddf1ff7f9126a11f2b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "564c7ccd87ca886f79f40db2feda663e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/events/device_event.dart", "hash": "aa3c9a130d6e7ac3aaa10045c29669b9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "fff8e299112c38dd03e2e918a84e52c0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "60baff7460853877cbb929f66b052733"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/toggleable.dart", "hash": "ef3c172e10ae4e723ca920d42664a09e"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/switch.dart", "hash": "ae22408527b727a26bd9e2e29e78c1ba"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "eb1f091dfc256bc49d734c8cea9210e8"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "3d97078429fca38a278d2fe38c9fbef3"}, {"path": "/sdks/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "76d339b78cfd7fa3b43da126d24f6662"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "b5172d7f7c209661c70b4574107cd774"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/delegates/gpu_delegate.dart", "hash": "9106808d2ace8c30f4f4af074a61c029"}, {"path": "/sdks/flutter/packages/flutter/lib/gestures.dart", "hash": "5276912c02c2652dae0fe549d6d4db3b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "eabaa11e875f30c638dbea199dc82f44"}, {"path": "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/src/grapheme_clusters/breaks.dart", "hash": "359388897ae53df8791213c31ef05fe6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "90d2e3f81c2871fed636143007914657"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/search.dart", "hash": "65d7efb36f1c0981aeb1aa5ca2dc981b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "31bef5128dcadbf7d3e638769a5ccb63"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "0fc01b4e9beb5706e036ee4dd2a18994"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "bfbcdf98f1800a358974017a5a244b89"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "7d3f2619c15bb38d440a3ae894893177"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "e6131583e11852cce5dc36b190644131"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "903d9a8ecbea5b745da3432d5197d5bf"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "311f57c9efa242be81f6bff4085b694f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "9a963571e5d30e052aba3137cb08afde"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "4f683ee2e1b110e70e51ebbf8db44352"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "c75c1b6e41b334befa8fea8f40a1012e"}, {"path": "/sdks/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "823c66be3a8d17bc0c146c6b7f83062c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "b7ae6a21a9a41ce292d8ce22be59102c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "7e48865e3a52a9c6fc91567a38b0bcef"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "34650bbb402bea0d9e0905df4adadaae"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "7f3b4e775cb1472996a0505423dd47f6"}, {"path": "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/src/grapheme_clusters/constants.dart", "hash": "9f9b79f577d9fdf4f20c17a26a2f1d57"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "011b312a6ef99efa780c4fa8136a56d3"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "b5b6d18d1e7d2f546507f52385a96d31"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "148c709e17c08f50d3daa670b9f91f12"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "61fc5d47f00a6477275a3102fea8cbf1"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "20956ccacef6e41926ba780a442ded4d"}, {"path": "/sdks/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "ac1235401776e9d976fcae06c4a1bf69"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "67a67689c5e814c245beae89edfa8ec5"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "449e5e076d88fdec0781d58650e1b713"}, {"path": "/sdks/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "1abea6cc1b12dbf5811214407d955b98"}, {"path": "/sdks/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "900f6993636e3b9ce2be24d38150722d"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/method_channel/type_conversion.dart", "hash": "cd53e402b561c57f3a01c952fde4a349"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/canonicalized_map.dart", "hash": "26bd7410bad665cd40432c609e68e6a9"}, {"path": "/root/.pub-cache/hosted/pub.dev/test_api-0.4.16/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/sdks/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "c7125c517aa8cac22da28ac6e36eef79"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/sdks/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "81e9a073bd391f4db986dc7bef2f63a3"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "775ae14a90b86bb0668e9fcf6e6b1b6e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "b9e3ed70ab5e8c760f86ccdb79a5d039"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/equality.dart", "hash": "bb96a0e40a5198f25ff6a8b1dfd62a58"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "8df20838abdc91f514eff154c048d540"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "74b9d525d5d35a3f13d7da102d6ddcfa"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "c703d5a115a88c99fc446006b39e554c"}, {"path": "/root/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "2981afacc7a836ccc661fd0be2eb0681"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "44919c3936c0c4f7fcbd5caab7d1a713"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "a6f86b07a971f37494568319ea7cb2c2"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/app.dart", "hash": "1824c826f0f8d8a85bda7e1f0bb1dead"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7/lib/src/utils.dart", "hash": "279ab400656a09dd3ac7ce8b38c5c4a6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "ff8176887d706346f1adf9b10d93223a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "c359a6e952958e57c58e53ead868196a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "0d00a012348d802a099ed222dd200d3c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d235f51d48e43d80a46b35d3ac1a7135"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "565097e561f05b1bfab2b3e5bb1bc476"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "6b1e7684c07bd93ed9433f5be917a56b"}, {"path": "/root/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.6/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "f09da56112028c2b23759b17c2128ffe"}, {"path": "/root/.pub-cache/hosted/pub.dev/ffi-2.0.2/lib/src/allocation.dart", "hash": "43afda18b80de74d64e5858c6e99760f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "8a2a6e194bea6968fce6409b962bf485"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "e6717e820802970026778f01d782a208"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "2d616b6da53dd3a5bdc86abaca3656c8"}, {"path": "/sdks/flutter/packages/flutter/lib/animation.dart", "hash": "b3dbc4e6cac78b2c99e3aa5e9e498ea1"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/iterable_extensions.dart", "hash": "5369274e0f2aea3db2ae228646de17c2"}, {"path": "/root/.pub-cache/hosted/pub.dev/meta-1.8.0/lib/meta.dart", "hash": "9ebddb9a0743de208281e764b9454978"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/method_channel/method_channel_camera.dart", "hash": "ca55baac33aae9ebf65da20b8380ebea"}, {"path": "/app/build/app/intermediates/flutter/release/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e528825eba95e29a4f2020bedddfdb2d"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/lib/src/type_conversion.dart", "hash": "e335dd6b7affb42713c782d5a95bca48"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "8ec86c62ef84ec90ddf5232d5843fd37"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "a9e643bffeac9afc79115446b7cb7c9f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "0c2a39fe9ac2a3573ea263d944a7d4cd"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "e8a1d220d69629ac54c287b5f096fbf8"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "ea2d0e58ef3ab5bb9ac77120ef2c1b53"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/camera_image_data.dart", "hash": "12cb420815f15492715656fe5a77f4a5"}, {"path": "/root/.pub-cache/hosted/pub.dev/js-0.6.5/LICENSE", "hash": "bfc483b9f818def1209e4faf830541ac"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "a1fd89ba2ce1c781946ebc5f37479b40"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "c74d6bfe6ed9493e9a15a793e240491f"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/hct/viewing_conditions.dart", "hash": "89ac6e1a99054eb05805c6e7a6f58e6f"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/media_settings.dart", "hash": "6a2886b3913c86d327624dfdccda7c04"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "5184895d365f664a514993d279af93c6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "2ffd4331d4ce18eb39bd200f76368dbf"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/model.dart", "hash": "5ff07d0da5084b8839cf69eda2ce913b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/card.dart", "hash": "1f408d5024cb1ebaff1bdc7d2d642b34"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "1e0b9c706c2f52ef8c68d71e059b368d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "323be88f9396112a89f1f6dae5b16551"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "b1d7b10a451a7a5680c4473652e4e3c0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "206832bc71a4fbdfd4a077b6f35e6333"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "563354fcceca6f2c834a5707c2cd9e13"}, {"path": "/sdks/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "93fccc5a13669d1df317968de5b2590c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "0a7c8ea575635aba0b835cbf35892f9b"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/tensor.dart", "hash": "293e048780b3baf7ec14b5f7aaae6d3e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "ebfa6ac15e746da747f5533d2a54c06b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "9510b7a2f095e041d6b01024407d92ae"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "6a65e9988b21768c80a49713778c2cd0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "2c25776d2e769ec49cc16f5058c5c009"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "f365fc9346ed92a347bd6659be254ab4"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_channel-2.1.1/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "669c88cbef458b3b049bad5ff3c2dfbf"}, {"path": "/root/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.6/assets/CupertinoIcons.ttf", "hash": "42d5bf7c22ac609351e84dbc39b12bf9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "d62b3af98fb22fa468d83954bf069d5f"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/scheme/scheme.dart", "hash": "f404c13ab6ba7fca8a465852af425026"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "bd3e86d27c38dca6dccbe5ac5e7c7663"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "542c329c98e02e449591773d3c7a7364"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/flash_mode.dart", "hash": "9faa11bfe2a421b6bcb9cea335eb31a3"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "96a77b3df76ebda77a5525db0c94a40e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "7e3a59cfc4c1fe568c6bddbca28625ab"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "42074e2b3250cd1eab474db9941ed08a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/time.dart", "hash": "4e84d0303aa781634ad57d9b48af9b00"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "c923a6390994ceeb31124b620be66886"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "cf72bd613d277b76bf935e6c5bfc157d"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/exposure_mode.dart", "hash": "830c503e2d20f6ce44495aa77cbd5e8c"}, {"path": "/root/.pub-cache/hosted/pub.dev/ffi-2.0.2/lib/ffi.dart", "hash": "d4f2e5f5bf05006a98a9ec38dfb91641"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "074b866f17aee09c76583b075e83cb8c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "4a91b5cf945c2bc9e2a4c289df61e55b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "3843f59e1c5febcb8443f79f1e8d4eee"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/collection.dart", "hash": "476383869aff7b87579a7753e47722d7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "7f34290bd4853b35be31761b6c7d3864"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "a6a5683319eaf061f2f8448162cb8885"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/quanitzation_params.dart", "hash": "35674beada00b2d6624db7d45c4944c2"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "c6f8b638d691a2bba7362cd598d93ef8"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "d84f409f2f0e14c957e42d8c74175a1a"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "ccb08191f9bcfb4f1a62504278ce3a1b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "bba71a5811a4c39e16cfd7087e7d5f82"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "0457a594f4c14181f802c54252615c81"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "c741a13227ee01cd4566adb6aae50122"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "9b9639fb37f3307761e011646c3a3ce0"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/combine_latest.dart", "hash": "3350190c3961f0375eaf08899e8a5817"}, {"path": "/app/build/app/intermediates/flutter/release/flutter_assets/AssetManifest.json", "hash": "fc38911d6505287c300c005353329221"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "d0885097eb9ccba90210852d52fc83e2"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "b7943d020a6c7b63a2483143ecf5f6d0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "46b0774ca33285fd90e42ad91bf706fd"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "8a380c86046c073f7ca67b32ce9ecec7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "80c27ebf6b325589047ce9dc21922955"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/algorithms.dart", "hash": "42e9d4d3462785bf39af0270d42722e8"}, {"path": "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/src/characters_impl.dart", "hash": "3bb0652e163327c58784ce2a2b882a7c"}, {"path": "/app/build/app/intermediates/flutter/release/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "42d5bf7c22ac609351e84dbc39b12bf9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "7f6c267be8df852637226ab1708d5417"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "7157fe770547a841f9b3e8f7b6765c4a"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/interpreter_options.dart", "hash": "7e8449b13673609831095fb6d056e16a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "fda9c6f48f58c23eab466cd8565a77fd"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "e8a46aad9b09df8e467cb1c6829ff35e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "18149c55b175306f694fd72c293e8a4d"}, {"path": "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "14c5e92c3e535ae197e3d7b784ba98c9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "355cd0132946f4de06b53d2d53d0f608"}, {"path": "/app/build/app/intermediates/flutter/release/flutter_assets/assets/models/gesture_model1.tflite", "hash": "8f5263f2727effe133bfb68bbad43c3c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "08b4e67b157b8770f50c3e1593586101"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/hct/hct_solver.dart", "hash": "33aa9632d38a7855ec7452224f80471c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "9ac2b8f7c9eba819c8aee376fdde1ad2"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "b699e128d86de6c400fab59bf5ed37cd"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "34805aed0d0a89fb081a78edc767bcca"}, {"path": "/root/.pub-cache/hosted/pub.dev/meta-1.8.0/lib/meta_meta.dart", "hash": "36280c072e87476893ba441b9b25bc39"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/material_color_utilities.dart", "hash": "efd86bd9a7183660b902f2528da33973"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "be7fd149b14f26a8cf158cc573e6f8b1"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "7d4e6658c86d17b6aad6ea88d538449c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "69c59af240ed814583dc1635986b09d6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "bfc66f055c8ce27beae0b3ae05350318"}, {"path": "/sdks/flutter/packages/flutter_tools/lib/src/build_system/targets/android.dart", "hash": "8a8d939c53ec719db25ed026b42de925"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "7d9bef290415f78dee8bc0fdd47025f2"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "d8221989cee24f5d97fddbb2a535b34f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "e3ed79a2455f7742d0f030dc4a7f7097"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "116458bf79c178c3089f12f2bd57e611"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "4b1ca4de6a5eee477b785b591d8a28f2"}, {"path": "/sdks/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "90c1fe2fd81558e20d588ef846f68876"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "cfdbbeecafa9afe958cc12f0363e0f2e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "3ffbd6fc0995838940a61cba006cbaa7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "7ec68a5bb3492720f51417187a4c483c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "55380226455ea534ad3f21ab09fa4cae"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "072675f16dc002f7be3914b33aa7d20b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "2fa5623be6e2680ff9befb49dbb5102f"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/priority_queue.dart", "hash": "6c66821ec0d637772e147c298645e478"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7/lib/camera_avfoundation.dart", "hash": "9d49f62361b0d2204c18bdeb0a34169c"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/video_capture_options.dart", "hash": "53381c0fce37ebe9151ad62f552563e1"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "fc6ac74a8ad779b64bf7e626552ecb2a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "711adece1d277c89c00ac553ca718ffd"}, {"path": "/app/build/app/intermediates/flutter/release/flutter_assets/FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "91480f38df85d142de41e5bd0b154d3f"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/tflite_flutter.dart", "hash": "dabc1e788ed1498df46bc813c7c5ed8c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "3ef55cc59bb88a61c8bf5f1ea2faadf4"}, {"path": "/root/.pub-cache/hosted/pub.dev/matcher-0.12.13/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/sdks/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "6656ba0c69fefef80b8cae101896c029"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "3bf69bfd4070ddbf2b64ce381c00975d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "4bac9f63bc7aa9c5e5104179e86c64a9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "d3e8015d1a7bccc3005d76a069edf012"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "9ac903cffbc9fc815782d8e0bcea7e64"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/resolution_preset.dart", "hash": "adc7260afdb040ea776752b5ef2b297b"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/async_map.dart", "hash": "225c8aa337b07f1ecc768e017fb7c5a3"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "1312aed2c5e59964982eb7c85c7430c3"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera-0.10.5+5/lib/src/camera_image.dart", "hash": "0a466c2102627c9df65864630aec34de"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/util/byte_conversion_utils.dart", "hash": "af0d8ab434759cafa68cb9880d5771cc"}, {"path": "/root/.pub-cache/hosted/pub.dev/stack_trace-1.11.0/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/sdks/flutter/packages/flutter/lib/foundation.dart", "hash": "4ce6f3c41ded0f3794fe3f0106ab971a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "c0e496ed40e0cb9154c5418657d2fa2e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "c87e19f7c886263df8586146fe147faf"}, {"path": "/app/.dart_tool/package_config_subset", "hash": "a49b76f8d986db94e0606a28b80d2943"}, {"path": "/sdks/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "79ed68fe678281de2561462a198ea941"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "06e25506dd42326f711f5a02366048ef"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "4a5960383439d4ef8fe8b42450ac3be5"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "05f3174f379af4ccd07fccea88487a40"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "2e897a9797ccadaa99c58bc96f4a1579"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "d46d2121000866bd902db8a26fa1fc3e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "24072c05f67aa1bd35a3389621d80c25"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "f712a6d1a54fde3b5c6c41dbe7d8d06a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "3007a85885c8729a7f98628f002c9439"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "3d54ff724ed3381d38104d6603406b8a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "3ec266c46c85603471447b253038693a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "580a1c9361f66db5377d1151704dc77c"}, {"path": "/root/.pub-cache/hosted/pub.dev/ffi-2.0.2/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/delegate.dart", "hash": "029a2b36502bc5e68f0fb38958c49c54"}, {"path": "/sdks/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "f13353269669d7b19e3768edaeda5caa"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "d493eee37b19039e0bbce0c76f9a5be9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "3de0b02c72ffa2dbcd4241d85896ffa7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "f51a4cccdeb513773de9af509f073dcf"}, {"path": "/root/.pub-cache/hosted/pub.dev/ffi-2.0.2/lib/src/utf8.dart", "hash": "673c6fc6ba5a1499362db4bad82b3f10"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "ac8e4f07fe57a180a83a79d97030a4ee"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "a1767e0bdb8f6eaebb569c18166e3459"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "a3aa38647fd73e6e6fa27450d34c042f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "6e40ff949fd347e34e4827db183ab0f7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "81859a4dbd7cc98b9025645ba344330b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "09f75080be8aa63c54de8f0f13d49397"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "5cfb987e1a4ea727dbbc7abb10e087a6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "7dc929eb036d49956284257b4e0d83d0"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/empty_unmodifiable_set.dart", "hash": "4814f7a1c3a7509ddb71fa9f13b2e169"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/wrappers.dart", "hash": "11e87c02beec338363a30c8f2f49c522"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "8bae075f6533238e997073d1c4342ee2"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/root/.pub-cache/hosted/pub.dev/path-1.8.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/root/.pub-cache/hosted/pub.dev/flutter_lints-2.0.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "d2a2d8377af7f577a4e2b4fadc6a638e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "38d7213cd28308ac1f0bd5d81544c794"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "d83aaf70ed7e980bc976d12b8f31a5c7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "c324914d8137036411457678f3c69dc0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "4da8a7e66b441d643caff99f743d9f69"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/utils/color_utils.dart", "hash": "1f336a83ee9c6cc00bf8a24d2376ebda"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "d7da19c57ae21b84b31832940bf36c95"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "265da611bd90e5b3f22b0e399086e1a8"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "4df1293c0ca4ba2371070c07770510a9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "911148240fa2017589039df8adbdbd7d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "ad3271502e45fbefbae041fe273fcc78"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "acf1a155ed6a399140bf98cbadc44ae7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "543079fc1d0c7864e7a179f3dcaeb44a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "99f23e7e1e32cbca7a688f042454f8fd"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "488d308a0cd45bf398708879506a91e3"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "5b539bedaf82a69ffd5c93fe8cb16465"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "1ba052cc7c20457f24cdff4d601afdd7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "54363b635c5af0166a92a944822afcdc"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "7656627b51b07565889084020b5078d6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "55699f42160ec7cd28e59b3d85b16679"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "eeca328d147c15b45c2f02a4093dee86"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "deccbe5e50fd283d096b079c3830b295"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "46b900fb75f7497e307a6213009349a4"}, {"path": "/app/lib/tflite_service_lite.dart", "hash": "45445adac8a02dbd170f64ce1bf0a600"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "7c46db5c279bfd34b41aee4e64ba88f1"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "e3a3e744d6ef8ddf1d75a37ba549eaf4"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "f627691ae693e673d0a28ee10fa7ef21"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "a5266adbd58aab45e34dc05f787ca535"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "faa4ca37423c4ebc1968a9922537ee82"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/LICENSE", "hash": "3b83ef96387f14655fc854ddc3c6bd57"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "cb595dc1e32ae1c22e55b0971067c9df"}, {"path": "/sdks/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "aa2543ffe35df101f33108e6243f5b9b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "7047d90229336cb3caa0fff5bb3dafa5"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "31c7443c821ddc575659a119a00bb42f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "b9b84bf4881138ace165fd23b39a89d6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "fce75150bcb08fa104fded6310750474"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "0bed54eea911a00191131999c1d9654d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "f2635eeaaf9379b39e0c48d203b260b1"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "8a899256e5ac320579b269ee9b2567a8"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/delegates/coreml_delegate.dart", "hash": "8d6a2421a2c826ca69fcbcb1ce406ef6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "49510c76201a05eba3c239a16896508b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "5bd42c05ffd396b9c61d5a82739deea4"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "8635fbec89c2cc03404a2a3233d31bbc"}, {"path": "/root/.pub-cache/hosted/pub.dev/source_span-1.9.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "4db80cc65121c34e697b03dee0d589f0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "63f89c285fda1a11134696c95a5d2c7c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "fcd0c8e5b3533ede79824283fd8c1717"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "406792dd8d248088cd84be85cf7c62fe"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/button.dart", "hash": "b04282840d037f7e4943e8ec88a12d02"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "e2dc9f40339f72e3b0e309272c5ceaa0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "9de25bd91716e1d1dc40d622d4694542"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "3fe6a46238f7699ff1039e43a3157acc"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/quantizer_celebi.dart", "hash": "b95b66e84724047193f602210d547054"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/palettes/tonal_palette.dart", "hash": "005fa9073603c7ee7b06d03658207e2a"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/take_until.dart", "hash": "c71f37842de1520aa4bb04b99bfc79a3"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "507fd70b4d332ca05a060defc0a30098"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "9d79df38c954cb1d6aef9a622fb16826"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "8865f4ba0316aa33cd4b8e48694ae276"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "99dd7740cb7ac228673d079cdd12490a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "881b5c11f52613550e6dbfcc45a2dc49"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/queue_list.dart", "hash": "368a628be82e16cf17725d00a83f582c"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/bindings/tensorflow_lite_bindings_generated.dart", "hash": "d9491ad0322976be6c76d2e2a9a08e19"}, {"path": "/app/.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/app.dill", "hash": "341d57cb08e0fc148614703919acb274"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "82f04582d6435fe28d0de62ac8646291"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "b1f861676f6495e6b884459f0d1ce964"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/date.dart", "hash": "8ebb687266ac064712bb7ff7158d106f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "e973169d55c1b0907b263fce8328bc84"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "187d36be28f01a2fb9747610a07b786c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "4511b6cbaa8c34b467724cd8753fd441"}, {"path": "/app/build/app/intermediates/flutter/release/armeabi-v7a/app.so", "hash": "1d036d663e2f4e86c273f07ed0440a22"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "fe48bdfc31adc4f7e657c36074278dca"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "09678623b04063ee4667ff9d46c9adf9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "034db2896155ca6ffedf49a9b6447708"}, {"path": "/sdks/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "08d323a9150badb65749c7e42ad05b8d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "59241e0339f42c809368fc054b767aba"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "e4b34d42cb8324d0485b25f1d3d9c1bf"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "ad4d82cd4898aebe901db7406316ab82"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "1e7180abd4c05b29d6f62162c16d57a2"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "32a7e3c5b9a8ba1f958c94f1c7ca6e36"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "fd037bb7e5e0417b1b57b2589825c2ed"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "b12267aa6ec80a40cec57548a12debf3"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "c8030797f5e00da5da18972f7f5c199e"}, {"path": "/root/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "38df7f10e1de0ca59f93b7e21e6428fc"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "f76941994ddf30e398313421f1588d85"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "9801ff6632dd0a6938381b54bc163196"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/platform_interface/camera_platform.dart", "hash": "76be9ed5a40c3cd182c50e8ff86e08ee"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "d678e87e7d7f75d05ddc20a878f28313"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "b03280f49bd251d8320667ffc244c613"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "08c2e37a2ae282c740bfe820f238eca1"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "9325b94e09a773e59eccfedc0dee4711"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "4c07a36206ab3ca200d52d7f24127233"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "a641dfaf4d467277c7075e54e33dc55c"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/focus_mode.dart", "hash": "abb2e92bce56af1b33c6ff3bee1ef235"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/ffi/helper.dart", "hash": "0a7bd1c7ce2bd05fb93894c59feb8df2"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/camera_platform_interface.dart", "hash": "6637bcaf11e4232d921cf5b718eff0cf"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/image_format_group.dart", "hash": "64f7d6a0aea16e1f13c99592ba8e0e08"}, {"path": "/root/.pub-cache/hosted/pub.dev/meta-1.8.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "63c306461db5c60830794d4def8b7e7b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "60b4523b7d94f2184414a314335fd486"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/score/score.dart", "hash": "95586aad0f0a7b13580767122c5ce9d8"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "a48ea636b7f2a32cdb71df23faa3451b"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/isolate_interpreter.dart", "hash": "de8b2ed009fb76dd6d64403fb06fa672"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "9916a2d85ee324e7b395bec41a75db58"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "fd48fe375392a882e621f95b35695b1d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "0cb06ef1fbbec09f85b6b40cdeaa2f9a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "886142a229b0dda691ec6b45f89b6db3"}, {"path": "/root/.pub-cache/hosted/pub.dev/clock-1.1.1/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "9e2dc24fb141c54394f013ec20569c50"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "8dcbf9c6d1b75bd1af118ae233e7639f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "7525ba8f2df029fe86fe08725d060d3e"}, {"path": "/root/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/check.dart", "hash": "1e349c70f2c5adf4db47912720fff037"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "383362c943d53b1e9cb7db84db0427f6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "0a120ca3aa00e2a367ad33648b49a1e5"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "58b20f802baf08f63dcbf4eae87a754e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "64a2261cfb426cbc1bf5f5985fed327d"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/scan.dart", "hash": "de155f165219b10ba272bd030794873f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "975c2b518f6bdf0f423f19ee2d20f037"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "96ed798bc7e796289016d6e02421839d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "db1783b3083765425632b2ca451dbbc8"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "ff2cc2a4a374dafed8b1d437480a6f07"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "08fd4267e134da8246d879f2e0690f32"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "d3f74d846a99e580843bade81eec9e2b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "e3595ce1e159aa04bc09f69c5168c454"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "3d637d82142ce722be3fff1b564a3a4d"}, {"path": "/root/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "559dc718c8701191a2b6ac606aa086c8"}, {"path": "/root/.pub-cache/hosted/pub.dev/cross_file-0.3.3+6/lib/src/x_file.dart", "hash": "ef25c8f7ff1f10a43eecb0a0f44b38ba"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "c70e6da8344437d843e7eb009fd18da1"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "0722b5d6745ce6645d6caf63b2aedd89"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "c93f10f7ab0acf4285085b5125dd1bc0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "ebb00b4189561689271ac8ff24815bc7"}, {"path": "/root/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "4f36e38eaf3608ec18c70c13942510bd"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "ecc7f9827d87dcf6cb23310e70449242"}, {"path": "/sdks/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "c0fe6462e3a08d6d6afbf4f66130d494"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "12e47d3f4b91a456d52a5e37594dcdb4"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "fe52344c0c5470d7f044c347de68987e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "2dfc9e555b9df3380a6502bdfab9527a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "0941b23f5841e31aad0d25b4ca38293b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "11a634821b3bce05dac94f3dabe52a75"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "dac77e15418b6e85ba8237aa7af31c09"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "386a816011478bba19bf6973ff137cfd"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "e525624c777f81ae3166844928814efd"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "20c06004fe8ded0341d6b7d4ddd9fc63"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "bf2738395eea842961e96f4203e03df1"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "bc1745cbace394e492a6f9c8050b91bc"}, {"path": "/sdks/flutter/packages/flutter/lib/cupertino.dart", "hash": "95c52f75b6732050b2b87ad920f3cf14"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "2da52edd1b7a1dcc8df44ef7e4601583"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "f82b335ee8dd9ad923f619f83c130ecb"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "5beb6330c4fbaf6bf4abd034e6b7b67c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "e053a4d625695a213cd7cad71553d131"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "82030078ff5d06d4efd4970a53c03669"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "3ebce28fe3575a15329d8dc59ee7ca4c"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/types.dart", "hash": "a7831fae6e69451af2c66ed10f3a42cb"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "462df06e63c35293d84890049542ddce"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/unmodifiable_wrappers.dart", "hash": "ca6dcb9cea6b00d39a1deba4017fbde5"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "59d07e46049ce7af5b20d0bc808aedcc"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "157add6fe297fe09245c48cb7a918b41"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "8c14eb65e1d80381cac744a2a237818a"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "11565ddf9ff6c5542734e969d7abdb9f"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/concatenate.dart", "hash": "537b34c9197f582abf848f5160c0cb3e"}, {"path": "/app/build/app/intermediates/flutter/release/flutter_assets/NOTICES.Z", "hash": "636f614d7b84c2e05568ba89b24140fb"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "e9383af731a68adb6b314ec5b1068a45"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "2775727a104b34e20ef16dc30f5baeaf"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "03f36dcee465715c1854d772cff3153a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "c7ad70d1b453c3200a2c25b6f5ca4604"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "e4323ae67bf5fad2112cf257b91b64d7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "2cecbc714aea63bc341bdeecb0a1d079"}, {"path": "/app/pubspec.yaml", "hash": "caefde03ee8794abc3572d14c98be48a"}, {"path": "/root/.pub-cache/hosted/pub.dev/cross_file-0.3.3+6/lib/src/types/base.dart", "hash": "86039b13313ad468f867bb5522411241"}, {"path": "/app/.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/arm64-v8a/app.so", "hash": "8f90e51c5ee3a795c856ce6db32e3f48"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "500547c3b30c59fe1ff3c4b559e52e84"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "94bcda3ece81c51b940128304723207b"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/union_set.dart", "hash": "a9d0ac8febdea9748b1e274e2453730d"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/camera_exception.dart", "hash": "52a3ddfb21477258870d933fed27f1a7"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "598a75ad8b988bb14ac22ad62acabc10"}, {"path": "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/characters.dart", "hash": "188d03c92376ce139ce247b0f9b0946e"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7/lib/src/avfoundation_camera.dart", "hash": "448576be66ce6998a08bea61a93909ad"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "288419bf7d5c385c3e0f777da93f9e75"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "5854a36494004175430d4b4355622d7d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "1770aee1a90fac6336e3664811736471"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "591e01a4cb4c63927e5c090b0d064505"}, {"path": "/sdks/flutter/packages/flutter/lib/material.dart", "hash": "88af7ef9b0a369709fd232b83942f5c6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "3dc027f858c390ed73078fb86ce17bdb"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "85f5251e2d480320cf86ef4464727d2f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "d97019cfa3be6371779fc0e65f2bc118"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "8589d430ab74676555c4aa6b8b442af3"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "90710e516bb97142b02e2fd5553bf85c"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "7ef61738fd4e99b34b3379c2f6cf3141"}, {"path": "/root/.pub-cache/hosted/pub.dev/cross_file-0.3.3+6/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "263d65011d90bee92deefb163c823890"}, {"path": "/sdks/flutter/packages/flutter/lib/painting.dart", "hash": "0b006f9af878b6cbe7e6fbad8ad05390"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "131899d6899385466c5c59327fd335a3"}, {"path": "/sdks/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "bef69459165c25c77ef86a22c5a37605"}, {"path": "/root/.pub-cache/hosted/pub.dev/quiver-3.2.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "c8515afa8e555bfec16e832598d4e5ab"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "42cc52074bf3141c7247f972797790e6"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "1871ae3488fd813309b5d8039333de92"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "ac6376ef781bca02855c6494af606fcd"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "bf44bc2460fa88f9386f3b647c10bb80"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "e6b1d4b1f712225ec2e095ded085195a"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera-0.10.5+5/lib/src/camera_controller.dart", "hash": "47386667a125a6735ab586d387c7cbc8"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "00b08c6f5f7fbeb394b25a63d950c395"}, {"path": "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/bindings/bindings.dart", "hash": "940822a26954e4ece81b3e060edc034a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "ff558eecb6fa7834f9f31113f0cc723a"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "e3cdba21732fe10342e87f1c90c75b77"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "bfcb8efae16b9f03e09ee2adbac97777"}, {"path": "/sdks/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "2f66fd533c14db9ae785e3fa83c39692"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "e501f7efeecfca84ff857bd4f80b3ec4"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "35c3a0e09f2dcf608b806f259c306883"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "fbdb7eedf9f48948986e303f68e36ebe"}, {"path": "/root/.pub-cache/hosted/pub.dev/camera_web-0.3.2+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "61c71bdda6ca0e69558b10c76f7f2d0b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "993df9991302d75495941ede09bf8122"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "6e92335d98b127f7240ff5294e257c26"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "ca0af41a91bd98a37c4671c6756df973"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "0491e1cca60da329c2e03c48abde07c9"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "282eed7f135fdf9ac7bed4ce4faf2431"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "ac2880d91a3333c4d0e0b9a152bc7935"}, {"path": "/sdks/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "7c18da4e6b74ad62497f3cd0d139507c"}, {"path": "/sdks/flutter/packages/flutter/lib/widgets.dart", "hash": "b0f02b65ee2c6c7bb94c4ca6f1f29a6b"}, {"path": "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/app/lib/main.dart", "hash": "9b49ea63b09caa0b35670b7387523d46"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "5ea6f3bff5e08b2791946afb64deadae"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/combined_wrappers/combined_map.dart", "hash": "13db4f76c4c3dacee24311db33dffb5a"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "fa6839623ecc7d14a4396269172b75fa"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "7154b3b2612920bcf76384c4336b75dc"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "aae04d9745367d56247b3ec312b3e293"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "80e7f6041ae080a76c34cbb7a363fc1a"}, {"path": "/root/.pub-cache/hosted/pub.dev/ffi-2.0.2/lib/src/utf16.dart", "hash": "80bef275fa3274c803905212efaa81ef"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "d236c79282a2d6cf63ec4d7097400744"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "21f20162e45e5a6ed9d59115951fff5e"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "ea534a294d4f0ad77470def3a308074d"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "251708085912ebabc0559c31d7f9ef09"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/page.dart", "hash": "2b6129307d0fa6394c84e3a4ac5136e5"}, {"path": "/sdks/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "bdb69d0ad764dcf2d42d8fca35fb9ade"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "1ef43d7012623d3fb787b55f72fb9d87"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "a5636cabd205b19f4d440f7ed4ab56b0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "c808f81414eea878b72b91f90e2858e2"}, {"path": "/root/.pub-cache/hosted/pub.dev/async-2.10.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/where.dart", "hash": "d3c097f0f438086af45ba85d23422e5f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "0f62d58980340b028d6c7a94848693ae"}, {"path": "/sdks/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "b3263d1ee9aaef112570bcd31aae9ca8"}, {"path": "/sdks/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "170fe4655f45b54388ab850399d92895"}, {"path": "/sdks/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "a64d04b7a2bbc33416829490aac5ad1f"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "72d0ca199b14f8ad245f36eede78e596"}, {"path": "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/rate_limit.dart", "hash": "ebf02908da7035aae24c1141a5a4c3d0"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "8abcecb81f99bbc024b3ed169c86b95b"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "9c094b5d96bcff2f7c3ce85ce3101679"}, {"path": "/sdks/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "362b1506a336f9a85d0eabb816b63060"}, {"path": "/root/.pub-cache/hosted/pub.dev/ffi-2.0.2/lib/src/arena.dart", "hash": "5b7493735652fbaf742b8ec9cf53abad"}, {"path": "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "9343b5b49309dcad89c11b6151526278"}, {"path": "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/comparators.dart", "hash": "d1410f48ac374235aaad55cba40bc4be"}]}