{"inputs": ["/app/.dart_tool/package_config_subset", "/sdks/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "/sdks/flutter/bin/internal/engine.version", "/sdks/flutter/bin/internal/engine.version", "/sdks/flutter/bin/internal/engine.version", "/app/lib/main.dart", "/app/.dart_tool/flutter_build/dart_plugin_registrant.dart", "/sdks/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "/sdks/flutter/packages/flutter/lib/material.dart", "/root/.pub-cache/hosted/pub.dev/camera-0.10.5+5/lib/camera.dart", "/app/lib/tflite_service_lite.dart", "/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/lib/camera_android.dart", "/root/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7/lib/camera_avfoundation.dart", "/sdks/flutter/packages/flutter/lib/src/material/about.dart", "/sdks/flutter/packages/flutter/lib/src/material/action_chip.dart", "/sdks/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons.dart", "/sdks/flutter/packages/flutter/lib/src/material/app.dart", "/sdks/flutter/packages/flutter/lib/src/material/app_bar.dart", "/sdks/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/arc.dart", "/sdks/flutter/packages/flutter/lib/src/material/autocomplete.dart", "/sdks/flutter/packages/flutter/lib/src/material/back_button.dart", "/sdks/flutter/packages/flutter/lib/src/material/badge.dart", "/sdks/flutter/packages/flutter/lib/src/material/badge_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/banner.dart", "/sdks/flutter/packages/flutter/lib/src/material/banner_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "/sdks/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "/sdks/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "/sdks/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/button.dart", "/sdks/flutter/packages/flutter/lib/src/material/button_bar.dart", "/sdks/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/button_style.dart", "/sdks/flutter/packages/flutter/lib/src/material/button_style_button.dart", "/sdks/flutter/packages/flutter/lib/src/material/button_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "/sdks/flutter/packages/flutter/lib/src/material/card.dart", "/sdks/flutter/packages/flutter/lib/src/material/card_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/checkbox.dart", "/sdks/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "/sdks/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/chip.dart", "/sdks/flutter/packages/flutter/lib/src/material/chip_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/choice_chip.dart", "/sdks/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "/sdks/flutter/packages/flutter/lib/src/material/color_scheme.dart", "/sdks/flutter/packages/flutter/lib/src/material/colors.dart", "/sdks/flutter/packages/flutter/lib/src/material/constants.dart", "/sdks/flutter/packages/flutter/lib/src/material/curves.dart", "/sdks/flutter/packages/flutter/lib/src/material/data_table.dart", "/sdks/flutter/packages/flutter/lib/src/material/data_table_source.dart", "/sdks/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/date.dart", "/sdks/flutter/packages/flutter/lib/src/material/date_picker.dart", "/sdks/flutter/packages/flutter/lib/src/material/debug.dart", "/sdks/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "/sdks/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "/sdks/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "/sdks/flutter/packages/flutter/lib/src/material/dialog.dart", "/sdks/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/divider.dart", "/sdks/flutter/packages/flutter/lib/src/material/divider_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/drawer.dart", "/sdks/flutter/packages/flutter/lib/src/material/drawer_header.dart", "/sdks/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/dropdown.dart", "/sdks/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "/sdks/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/elevated_button.dart", "/sdks/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "/sdks/flutter/packages/flutter/lib/src/material/expand_icon.dart", "/sdks/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "/sdks/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "/sdks/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/feedback.dart", "/sdks/flutter/packages/flutter/lib/src/material/filled_button.dart", "/sdks/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/filter_chip.dart", "/sdks/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "/sdks/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "/sdks/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "/sdks/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/flutter_logo.dart", "/sdks/flutter/packages/flutter/lib/src/material/grid_tile.dart", "/sdks/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "/sdks/flutter/packages/flutter/lib/src/material/icon_button.dart", "/sdks/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/icons.dart", "/sdks/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "/sdks/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "/sdks/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "/sdks/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "/sdks/flutter/packages/flutter/lib/src/material/ink_splash.dart", "/sdks/flutter/packages/flutter/lib/src/material/ink_well.dart", "/sdks/flutter/packages/flutter/lib/src/material/input_border.dart", "/sdks/flutter/packages/flutter/lib/src/material/input_chip.dart", "/sdks/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "/sdks/flutter/packages/flutter/lib/src/material/input_decorator.dart", "/sdks/flutter/packages/flutter/lib/src/material/list_tile.dart", "/sdks/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/magnifier.dart", "/sdks/flutter/packages/flutter/lib/src/material/material.dart", "/sdks/flutter/packages/flutter/lib/src/material/material_button.dart", "/sdks/flutter/packages/flutter/lib/src/material/material_localizations.dart", "/sdks/flutter/packages/flutter/lib/src/material/material_state.dart", "/sdks/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "/sdks/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "/sdks/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/menu_style.dart", "/sdks/flutter/packages/flutter/lib/src/material/menu_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "/sdks/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "/sdks/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "/sdks/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "/sdks/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/no_splash.dart", "/sdks/flutter/packages/flutter/lib/src/material/outlined_button.dart", "/sdks/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/page.dart", "/sdks/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "/sdks/flutter/packages/flutter/lib/src/material/popup_menu.dart", "/sdks/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "/sdks/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/radio.dart", "/sdks/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "/sdks/flutter/packages/flutter/lib/src/material/radio_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/range_slider.dart", "/sdks/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "/sdks/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "/sdks/flutter/packages/flutter/lib/src/material/scaffold.dart", "/sdks/flutter/packages/flutter/lib/src/material/scrollbar.dart", "/sdks/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/search.dart", "/sdks/flutter/packages/flutter/lib/src/material/segmented_button.dart", "/sdks/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/selectable_text.dart", "/sdks/flutter/packages/flutter/lib/src/material/selection_area.dart", "/sdks/flutter/packages/flutter/lib/src/material/shadows.dart", "/sdks/flutter/packages/flutter/lib/src/material/slider.dart", "/sdks/flutter/packages/flutter/lib/src/material/slider_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/snack_bar.dart", "/sdks/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/stepper.dart", "/sdks/flutter/packages/flutter/lib/src/material/switch.dart", "/sdks/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "/sdks/flutter/packages/flutter/lib/src/material/switch_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/tab_controller.dart", "/sdks/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "/sdks/flutter/packages/flutter/lib/src/material/tabs.dart", "/sdks/flutter/packages/flutter/lib/src/material/text_button.dart", "/sdks/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/text_field.dart", "/sdks/flutter/packages/flutter/lib/src/material/text_form_field.dart", "/sdks/flutter/packages/flutter/lib/src/material/text_selection.dart", "/sdks/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "/sdks/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "/sdks/flutter/packages/flutter/lib/src/material/text_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/theme_data.dart", "/sdks/flutter/packages/flutter/lib/src/material/time.dart", "/sdks/flutter/packages/flutter/lib/src/material/time_picker.dart", "/sdks/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "/sdks/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/toggleable.dart", "/sdks/flutter/packages/flutter/lib/src/material/tooltip.dart", "/sdks/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "/sdks/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "/sdks/flutter/packages/flutter/lib/src/material/typography.dart", "/sdks/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "/sdks/flutter/packages/flutter/lib/widgets.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/camera_platform_interface.dart", "/root/.pub-cache/hosted/pub.dev/camera-0.10.5+5/lib/src/camera_controller.dart", "/root/.pub-cache/hosted/pub.dev/camera-0.10.5+5/lib/src/camera_image.dart", "/root/.pub-cache/hosted/pub.dev/camera-0.10.5+5/lib/src/camera_preview.dart", "/sdks/flutter/packages/flutter/lib/services.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/tflite_flutter.dart", "/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/lib/src/android_camera.dart", "/root/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7/lib/src/avfoundation_camera.dart", "/sdks/flutter/packages/flutter/lib/foundation.dart", "/sdks/flutter/packages/flutter/lib/scheduler.dart", "/sdks/flutter/packages/flutter/lib/cupertino.dart", "/sdks/flutter/packages/flutter/lib/rendering.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "/sdks/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "/sdks/flutter/packages/flutter/lib/animation.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "/sdks/flutter/packages/flutter/lib/gestures.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/material_color_utilities.dart", "/sdks/flutter/packages/flutter/lib/painting.dart", "/sdks/flutter/packages/flutter/lib/semantics.dart", "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/characters.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/actions.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/app.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/async.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/autofill.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/banner.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/basic.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/binding.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/container.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/debug.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/form.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/framework.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/heroes.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/icon.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/image.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/localizations.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/media_query.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/navigator.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/overlay.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/page_view.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/pages.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/restoration.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/router.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/routes.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/sliver.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/spacer.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/table.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/text.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/texture.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/title.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/transitions.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/viewport.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/visibility.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "/root/.pub-cache/hosted/pub.dev/cross_file-0.3.3+6/lib/cross_file.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/events/camera_event.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/events/device_event.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/platform_interface/camera_platform.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/media_settings.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/types.dart", "/sdks/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "/sdks/flutter/packages/flutter/lib/src/services/autofill.dart", "/sdks/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "/sdks/flutter/packages/flutter/lib/src/services/binding.dart", "/sdks/flutter/packages/flutter/lib/src/services/clipboard.dart", "/sdks/flutter/packages/flutter/lib/src/services/debug.dart", "/sdks/flutter/packages/flutter/lib/src/services/deferred_component.dart", "/sdks/flutter/packages/flutter/lib/src/services/font_loader.dart", "/sdks/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "/sdks/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "/sdks/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "/sdks/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "/sdks/flutter/packages/flutter/lib/src/services/message_codec.dart", "/sdks/flutter/packages/flutter/lib/src/services/message_codecs.dart", "/sdks/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "/sdks/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "/sdks/flutter/packages/flutter/lib/src/services/platform_channel.dart", "/sdks/flutter/packages/flutter/lib/src/services/platform_views.dart", "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "/sdks/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "/sdks/flutter/packages/flutter/lib/src/services/restoration.dart", "/sdks/flutter/packages/flutter/lib/src/services/service_extensions.dart", "/sdks/flutter/packages/flutter/lib/src/services/spell_check.dart", "/sdks/flutter/packages/flutter/lib/src/services/system_channels.dart", "/sdks/flutter/packages/flutter/lib/src/services/system_chrome.dart", "/sdks/flutter/packages/flutter/lib/src/services/system_navigator.dart", "/sdks/flutter/packages/flutter/lib/src/services/system_sound.dart", "/sdks/flutter/packages/flutter/lib/src/services/text_boundary.dart", "/sdks/flutter/packages/flutter/lib/src/services/text_editing.dart", "/sdks/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "/sdks/flutter/packages/flutter/lib/src/services/text_formatter.dart", "/sdks/flutter/packages/flutter/lib/src/services/text_input.dart", "/sdks/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "/root/.pub-cache/hosted/pub.dev/ffi-2.0.2/lib/ffi.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/bindings/bindings.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/delegate.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/delegates/gpu_delegate.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/delegates/metal_delegate.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/delegates/xnnpack_delegate.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/delegates/coreml_delegate.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/interpreter.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/interpreter_options.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/isolate_interpreter.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/quanitzation_params.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/tensor.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/util/byte_conversion_utils.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/util/list_shape_extension.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/stream_transform.dart", "/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/lib/src/type_conversion.dart", "/root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/lib/src/utils.dart", "/root/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7/lib/src/type_conversion.dart", "/root/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7/lib/src/utils.dart", "/root/.pub-cache/hosted/pub.dev/meta-1.8.0/lib/meta.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/annotations.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/assertions.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/binding.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/collections.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/constants.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/debug.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/isolates.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/key.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/licenses.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/math.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/node.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/object.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/platform.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/print.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/serialization.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/unicode.dart", "/sdks/flutter/packages/flutter/lib/src/scheduler/binding.dart", "/sdks/flutter/packages/flutter/lib/src/scheduler/debug.dart", "/sdks/flutter/packages/flutter/lib/src/scheduler/priority.dart", "/sdks/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "/sdks/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/app.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/button.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/colors.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/constants.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/debug.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/icons.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/picker.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/route.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/slider.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/switch.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/theme.dart", "/sdks/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/binding.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/box.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/debug.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/editable.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/error.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/flex.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/flow.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/image.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/layer.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/list_body.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/object.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/selection.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/sliver.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/stack.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/table.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/table_border.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/texture.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/tweens.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/view.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/viewport.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "/sdks/flutter/packages/flutter/lib/src/rendering/wrap.dart", "/sdks/flutter/packages/flutter/lib/src/animation/animation.dart", "/sdks/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "/sdks/flutter/packages/flutter/lib/src/animation/animations.dart", "/sdks/flutter/packages/flutter/lib/src/animation/curves.dart", "/sdks/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "/sdks/flutter/packages/flutter/lib/src/animation/tween.dart", "/sdks/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "/root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/arena.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/binding.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/constants.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/converter.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/debug.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/drag.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/eager.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/events.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/force_press.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/long_press.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/multitap.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/resampler.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/scale.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/tap.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/team.dart", "/sdks/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/blend/blend.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/hct/cam16.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/hct/hct.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/hct/viewing_conditions.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/palettes/core_palette.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/palettes/tonal_palette.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/quantizer.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/quantizer_celebi.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/quantizer_map.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/quantizer_wsmeans.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/quantizer_wu.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/scheme/scheme.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/score/score.dart", "/sdks/flutter/packages/flutter/lib/src/painting/alignment.dart", "/sdks/flutter/packages/flutter/lib/src/painting/basic_types.dart", "/sdks/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "/sdks/flutter/packages/flutter/lib/src/painting/binding.dart", "/sdks/flutter/packages/flutter/lib/src/painting/border_radius.dart", "/sdks/flutter/packages/flutter/lib/src/painting/borders.dart", "/sdks/flutter/packages/flutter/lib/src/painting/box_border.dart", "/sdks/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "/sdks/flutter/packages/flutter/lib/src/painting/box_fit.dart", "/sdks/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "/sdks/flutter/packages/flutter/lib/src/painting/circle_border.dart", "/sdks/flutter/packages/flutter/lib/src/painting/clip.dart", "/sdks/flutter/packages/flutter/lib/src/painting/colors.dart", "/sdks/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "/sdks/flutter/packages/flutter/lib/src/painting/debug.dart", "/sdks/flutter/packages/flutter/lib/src/painting/decoration.dart", "/sdks/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "/sdks/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "/sdks/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "/sdks/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "/sdks/flutter/packages/flutter/lib/src/painting/geometry.dart", "/sdks/flutter/packages/flutter/lib/src/painting/gradient.dart", "/sdks/flutter/packages/flutter/lib/src/painting/image_cache.dart", "/sdks/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "/sdks/flutter/packages/flutter/lib/src/painting/image_provider.dart", "/sdks/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "/sdks/flutter/packages/flutter/lib/src/painting/image_stream.dart", "/sdks/flutter/packages/flutter/lib/src/painting/inline_span.dart", "/sdks/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "/sdks/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "/sdks/flutter/packages/flutter/lib/src/painting/oval_border.dart", "/sdks/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "/sdks/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "/sdks/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "/sdks/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "/sdks/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "/sdks/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "/sdks/flutter/packages/flutter/lib/src/painting/star_border.dart", "/sdks/flutter/packages/flutter/lib/src/painting/strut_style.dart", "/sdks/flutter/packages/flutter/lib/src/painting/text_painter.dart", "/sdks/flutter/packages/flutter/lib/src/painting/text_span.dart", "/sdks/flutter/packages/flutter/lib/src/painting/text_style.dart", "/sdks/flutter/packages/flutter/lib/src/semantics/binding.dart", "/sdks/flutter/packages/flutter/lib/src/semantics/debug.dart", "/sdks/flutter/packages/flutter/lib/src/semantics/semantics.dart", "/sdks/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "/sdks/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/src/characters.dart", "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/src/extensions.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/constants.dart", "/sdks/flutter/packages/flutter/lib/physics.dart", "/sdks/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "/root/.pub-cache/hosted/pub.dev/cross_file-0.3.3+6/lib/src/x_file.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/utils/utils.dart", "/root/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.6/lib/plugin_platform_interface.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/method_channel/method_channel_camera.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/resolution_preset.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/camera_description.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/camera_exception.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/camera_image_data.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/exposure_mode.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/flash_mode.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/focus_mode.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/image_format_group.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/types/video_capture_options.dart", "/sdks/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "/root/.pub-cache/hosted/pub.dev/ffi-2.0.2/lib/src/allocation.dart", "/root/.pub-cache/hosted/pub.dev/ffi-2.0.2/lib/src/arena.dart", "/root/.pub-cache/hosted/pub.dev/ffi-2.0.2/lib/src/utf8.dart", "/root/.pub-cache/hosted/pub.dev/ffi-2.0.2/lib/src/utf16.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/bindings/tensorflow_lite_bindings_generated.dart", "/root/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/check.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/ffi/helper.dart", "/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.10.4/lib/src/model.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/async_expand.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/async_map.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/combine_latest.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/concatenate.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/merge.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/rate_limit.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/scan.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/switch.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/take_until.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/tap.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/where.dart", "/root/.pub-cache/hosted/pub.dev/meta-1.8.0/lib/meta_meta.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "/sdks/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/collection.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/utils/math_utils.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/utils/color_utils.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/hct/hct_solver.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/point_provider_lab.dart", "/root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/quantize/point_provider.dart", "/sdks/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/src/characters_impl.dart", "/sdks/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "/sdks/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "/sdks/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "/sdks/flutter/packages/flutter/lib/src/physics/simulation.dart", "/sdks/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "/sdks/flutter/packages/flutter/lib/src/physics/tolerance.dart", "/sdks/flutter/packages/flutter/lib/src/physics/utils.dart", "/root/.pub-cache/hosted/pub.dev/cross_file-0.3.3+6/lib/src/types/io.dart", "/root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/src/method_channel/type_conversion.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/aggregate_sample.dart", "/root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/src/from_handlers.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/algorithms.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/boollist.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/canonicalized_map.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/combined_wrappers/combined_iterable.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/combined_wrappers/combined_list.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/combined_wrappers/combined_map.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/comparators.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/equality.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/equality_map.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/equality_set.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/functions.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/iterable_extensions.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/iterable_zip.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/list_extensions.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/priority_queue.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/queue_list.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/union_set.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/union_set_controller.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/unmodifiable_wrappers.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/wrappers.dart", "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/src/grapheme_clusters/table.dart", "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/src/grapheme_clusters/constants.dart", "/root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/src/grapheme_clusters/breaks.dart", "/root/.pub-cache/hosted/pub.dev/cross_file-0.3.3+6/lib/src/types/base.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/utils.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/combined_wrappers/combined_iterator.dart", "/root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/src/empty_unmodifiable_set.dart"], "outputs": ["/app/.dart_tool/flutter_build/41b1012c3b12c1ebd5da6353e774ef70/app.dill"]}