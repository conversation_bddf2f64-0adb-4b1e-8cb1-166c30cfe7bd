hello_world_app
2.19
file:///app/
file:///app/lib/
async
2.18
file:///root/.pub-cache/hosted/pub.dev/async-2.10.0/
file:///root/.pub-cache/hosted/pub.dev/async-2.10.0/lib/
boolean_selector
2.17
file:///root/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/
file:///root/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/lib/
camera
2.19
file:///root/.pub-cache/hosted/pub.dev/camera-0.10.5+5/
file:///root/.pub-cache/hosted/pub.dev/camera-0.10.5+5/lib/
camera_android
2.19
file:///root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/
file:///root/.pub-cache/hosted/pub.dev/camera_android-0.10.8+13/lib/
camera_avfoundation
2.19
file:///root/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7/
file:///root/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.13+7/lib/
camera_platform_interface
2.19
file:///root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/
file:///root/.pub-cache/hosted/pub.dev/camera_platform_interface-2.6.0/lib/
camera_web
2.19
file:///root/.pub-cache/hosted/pub.dev/camera_web-0.3.2+2/
file:///root/.pub-cache/hosted/pub.dev/camera_web-0.3.2+2/lib/
characters
2.12
file:///root/.pub-cache/hosted/pub.dev/characters-1.2.1/
file:///root/.pub-cache/hosted/pub.dev/characters-1.2.1/lib/
clock
2.12
file:///root/.pub-cache/hosted/pub.dev/clock-1.1.1/
file:///root/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/
collection
2.12
file:///root/.pub-cache/hosted/pub.dev/collection-1.17.0/
file:///root/.pub-cache/hosted/pub.dev/collection-1.17.0/lib/
cross_file
2.19
file:///root/.pub-cache/hosted/pub.dev/cross_file-0.3.3+6/
file:///root/.pub-cache/hosted/pub.dev/cross_file-0.3.3+6/lib/
cupertino_icons
2.19
file:///root/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.6/
file:///root/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.6/lib/
fake_async
2.12
file:///root/.pub-cache/hosted/pub.dev/fake_async-1.3.1/
file:///root/.pub-cache/hosted/pub.dev/fake_async-1.3.1/lib/
ffi
2.17
file:///root/.pub-cache/hosted/pub.dev/ffi-2.0.2/
file:///root/.pub-cache/hosted/pub.dev/ffi-2.0.2/lib/
flutter_lints
2.19
file:///root/.pub-cache/hosted/pub.dev/flutter_lints-2.0.3/
file:///root/.pub-cache/hosted/pub.dev/flutter_lints-2.0.3/lib/
flutter_plugin_android_lifecycle
2.19
file:///root/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.17/
file:///root/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.17/lib/
js
2.16
file:///root/.pub-cache/hosted/pub.dev/js-0.6.5/
file:///root/.pub-cache/hosted/pub.dev/js-0.6.5/lib/
lints
2.17
file:///root/.pub-cache/hosted/pub.dev/lints-2.0.1/
file:///root/.pub-cache/hosted/pub.dev/lints-2.0.1/lib/
matcher
2.18
file:///root/.pub-cache/hosted/pub.dev/matcher-0.12.13/
file:///root/.pub-cache/hosted/pub.dev/matcher-0.12.13/lib/
material_color_utilities
2.13
file:///root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/
file:///root/.pub-cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/
meta
2.12
file:///root/.pub-cache/hosted/pub.dev/meta-1.8.0/
file:///root/.pub-cache/hosted/pub.dev/meta-1.8.0/lib/
path
2.12
file:///root/.pub-cache/hosted/pub.dev/path-1.8.2/
file:///root/.pub-cache/hosted/pub.dev/path-1.8.2/lib/
plugin_platform_interface
2.19
file:///root/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.6/
file:///root/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.6/lib/
quiver
2.17
file:///root/.pub-cache/hosted/pub.dev/quiver-3.2.2/
file:///root/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/
source_span
2.14
file:///root/.pub-cache/hosted/pub.dev/source_span-1.9.1/
file:///root/.pub-cache/hosted/pub.dev/source_span-1.9.1/lib/
stack_trace
2.18
file:///root/.pub-cache/hosted/pub.dev/stack_trace-1.11.0/
file:///root/.pub-cache/hosted/pub.dev/stack_trace-1.11.0/lib/
stream_channel
2.14
file:///root/.pub-cache/hosted/pub.dev/stream_channel-2.1.1/
file:///root/.pub-cache/hosted/pub.dev/stream_channel-2.1.1/lib/
stream_transform
2.14
file:///root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/
file:///root/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/
string_scanner
2.18
file:///root/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/
file:///root/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/
term_glyph
2.12
file:///root/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/
file:///root/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/
test_api
2.18
file:///root/.pub-cache/hosted/pub.dev/test_api-0.4.16/
file:///root/.pub-cache/hosted/pub.dev/test_api-0.4.16/lib/
tflite_flutter
2.12
file:///root/.pub-cache/hosted/pub.dev/tflite_flutter-0.9.5/
file:///root/.pub-cache/hosted/pub.dev/tflite_flutter-0.9.5/lib/
vector_math
2.14
file:///root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///root/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
sky_engine
2.12
file:///sdks/flutter/bin/cache/pkg/sky_engine/
file:///sdks/flutter/bin/cache/pkg/sky_engine/lib/
flutter
2.17
file:///sdks/flutter/packages/flutter/
file:///sdks/flutter/packages/flutter/lib/
flutter_test
2.17
file:///sdks/flutter/packages/flutter_test/
file:///sdks/flutter/packages/flutter_test/lib/
flutter_web_plugins
2.17
file:///sdks/flutter/packages/flutter_web_plugins/
file:///sdks/flutter/packages/flutter_web_plugins/lib/
2
