<libraries>
  <library
      name="__local_aars__:/app/build/app/intermediates/flutter/release/libs.jar:unspecified@jar"
      jars="/app/build/app/intermediates/flutter/release/libs.jar"
      resolved="__local_aars__:/app/build/app/intermediates/flutter/release/libs.jar:unspecified"/>
  <library
      name="/app/android@@:flutter_plugin_android_lifecycle::release"
      jars="/app/build/flutter_plugin_android_lifecycle/.transforms/2f89db70a99a6425fe1b63eeba56d40c/transformed/out/jars/classes.jar"
      resolved="io.flutter.plugins.flutter_plugin_android_lifecycle:flutter_plugin_android_lifecycle:unspecified"
      folder="/app/build/flutter_plugin_android_lifecycle/.transforms/2f89db70a99a6425fe1b63eeba56d40c/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="/app/android@@:camera_android::release"
      jars="/app/build/camera_android/.transforms/abec00fccb875761d19e96f09e97ae4b/transformed/out/jars/classes.jar"
      resolved="io.flutter.plugins.camera:camera_android:unspecified"
      folder="/app/build/camera_android/.transforms/abec00fccb875761d19e96f09e97ae4b/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="/app/android@@:tflite_flutter::release"
      jars="/app/build/tflite_flutter/.transforms/2a3be701b62962aa7f85b533b9d2b854/transformed/out/jars/classes.jar"
      resolved="com.tfliteflutter.tflite_flutter_plugin:tflite_flutter:unspecified"
      folder="/app/build/tflite_flutter/.transforms/2a3be701b62962aa7f85b533b9d2b854/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.flutter:flutter_embedding_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/io.flutter/flutter_embedding_release/1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e/f73a182cf8c03bf2175c268e83b14e8032de7fe3/flutter_embedding_release-1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e.jar"
      resolved="io.flutter:flutter_embedding_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e"/>
  <library
      name="androidx.window:window-java:1.0.0-beta04@aar"
      jars="/root/.gradle/caches/transforms-3/006d93cd422dbc43e89591147c2de572/transformed/jetified-window-java-1.0.0-beta04/jars/classes.jar"
      resolved="androidx.window:window-java:1.0.0-beta04"
      folder="/root/.gradle/caches/transforms-3/006d93cd422dbc43e89591147c2de572/transformed/jetified-window-java-1.0.0-beta04"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window:1.0.0-beta04@aar"
      jars="/root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/jars/classes.jar"
      resolved="androidx.window:window:1.0.0-beta04"
      folder="/root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-android/1.5.2/d246a704a55b7bddb79407cce4348890eaa341d9/kotlinx-coroutines-android-1.5.2.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.2@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.5.2/f4cc07a50437659e0043e7da762809a46932b6a0/kotlinx-coroutines-core-jvm-1.5.2.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.5.30@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.5.30/5fd47535cc85f9e24996f939c2de6583991481b0/kotlin-stdlib-jdk8-1.5.30.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.5.30"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/1.7.10/1ef73fee66f45d52c67e2aca12fd945dbe0659bf/kotlin-stdlib-jdk7-1.7.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10"/>
  <library
      name="io.flutter:armeabi_v7a_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/io.flutter/armeabi_v7a_release/1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e/48f7d6175f2d2f4ec04772ebd8c6f3587f674f70/armeabi_v7a_release-1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e.jar"
      resolved="io.flutter:armeabi_v7a_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e"/>
  <library
      name="io.flutter:arm64_v8a_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/io.flutter/arm64_v8a_release/1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e/8bdc0f5c6236952f4942d8fd843ecb67ef6713e3/arm64_v8a_release-1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e.jar"
      resolved="io.flutter:arm64_v8a_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e"/>
  <library
      name="io.flutter:x86_64_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/io.flutter/x86_64_release/1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e/bd519b1fddd75b32354a2128e7c4cd190aaf0c1c/x86_64_release-1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e.jar"
      resolved="io.flutter:x86_64_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.2.0@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common-java8/2.2.0/cd3478503da69b1a7e0319bd2d1389943db9b364/lifecycle-common-java8-2.2.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.2.0"/>
  <library
      name="androidx.fragment:fragment:1.1.0@aar"
      jars="/root/.gradle/caches/transforms-3/9354257804af8250f2ffb7c22e2892d5/transformed/fragment-1.1.0/jars/classes.jar"
      resolved="androidx.fragment:fragment:1.1.0"
      folder="/root/.gradle/caches/transforms-3/9354257804af8250f2ffb7c22e2892d5/transformed/fragment-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/f156363817d5f71529d60b6716392550/transformed/viewpager-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="/root/.gradle/caches/transforms-3/f156363817d5f71529d60b6716392550/transformed/viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/cf24d35e50a727536c7357721dc007c5/transformed/loader-1.0.0/jars/classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="/root/.gradle/caches/transforms-3/cf24d35e50a727536c7357721dc007c5/transformed/loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/252fc848d5aa6e267df5e6907a023aed/transformed/jetified-activity-1.0.0/jars/classes.jar"
      resolved="androidx.activity:activity:1.0.0"
      folder="/root/.gradle/caches/transforms-3/252fc848d5aa6e267df5e6907a023aed/transformed/jetified-activity-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/f4c2d181f9683c57f8970b9ce7785060/transformed/customview-1.0.0/jars/classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="/root/.gradle/caches/transforms-3/f4c2d181f9683c57f8970b9ce7785060/transformed/customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.6.0@aar"
      jars="/root/.gradle/caches/transforms-3/4ca28c4a71705f8a687c69dda140a277/transformed/core-1.6.0/jars/classes.jar"
      resolved="androidx.core:core:1.6.0"
      folder="/root/.gradle/caches/transforms-3/4ca28c4a71705f8a687c69dda140a277/transformed/core-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.2.0@aar"
      jars="/root/.gradle/caches/transforms-3/af4014bd5e02c19d06abc41b9651d21a/transformed/lifecycle-runtime-2.2.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.2.0"
      folder="/root/.gradle/caches/transforms-3/af4014bd5e02c19d06abc41b9651d21a/transformed/lifecycle-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/95578c0d327ed1d1ac84d08493ede1cf/transformed/jetified-savedstate-1.0.0/jars/classes.jar"
      resolved="androidx.savedstate:savedstate:1.0.0"
      folder="/root/.gradle/caches/transforms-3/95578c0d327ed1d1ac84d08493ede1cf/transformed/jetified-savedstate-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/7f4e81502f72dd837dc1e0ddcbb11c26/transformed/lifecycle-livedata-2.0.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.0.0"
      folder="/root/.gradle/caches/transforms-3/7f4e81502f72dd837dc1e0ddcbb11c26/transformed/lifecycle-livedata-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/3343d1c6adfed9e861db7c3f7f2df83a/transformed/lifecycle-livedata-core-2.0.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.0.0"
      folder="/root/.gradle/caches/transforms-3/3343d1c6adfed9e861db7c3f7f2df83a/transformed/lifecycle-livedata-core-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.2.0@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.2.0/4ef09a745007778eef83b92f8f23987a8ea59496/lifecycle-common-2.2.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.2.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/e5168ae1d5ab03fe8bf4ee87d623deae/transformed/core-runtime-2.0.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.0.0"
      folder="/root/.gradle/caches/transforms-3/e5168ae1d5ab03fe8bf4ee87d623deae/transformed/core-runtime-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.1.0@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.1.0/b3152fc64428c9354344bd89848ecddc09b6f07e/core-common-2.1.0.jar"
      resolved="androidx.arch.core:core-common:2.1.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/root/.gradle/caches/transforms-3/5abdc06e026f2f7b9b89c25dcd7a6d7e/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/root/.gradle/caches/transforms-3/5abdc06e026f2f7b9b89c25dcd7a6d7e/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.1.0/1f27220b47669781457de0d600849a5de0e89909/collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.1.0@aar"
      jars="/root/.gradle/caches/transforms-3/282676124ae0776b5645a1cee909808d/transformed/lifecycle-viewmodel-2.1.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.1.0"
      folder="/root/.gradle/caches/transforms-3/282676124ae0776b5645a1cee909808d/transformed/lifecycle-viewmodel-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.7.0@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation-jvm/1.7.0/920472d40adcdef5e18708976b3e314f9a636fcd/annotation-jvm-1.7.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.7.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.7.10@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.7.10/d2abf9e77736acc4450dc4a3f707fa2c10f5099d/kotlin-stdlib-1.7.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.7.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-common/1.7.10/bac80c520d0a9e3f3673bc2658c6ed02ef45a76a/kotlin-stdlib-common-1.7.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10"/>
  <library
      name="org.jetbrains:annotations:13.0@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/13.0/919f0dfe192fb4e063e7dacadee7f8bb9a2672a9/annotations-13.0.jar"
      resolved="org.jetbrains:annotations:13.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.1.0@aar"
      jars="/root/.gradle/caches/transforms-3/8965c941cf4bee4d72541b965c7c9d39/transformed/jetified-annotation-experimental-1.1.0/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.1.0"
      folder="/root/.gradle/caches/transforms-3/8965c941cf4bee4d72541b965c7c9d39/transformed/jetified-annotation-experimental-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/5b75d2e6479f13c5aed7d8001da507c0/transformed/jetified-tracing-1.0.0/jars/classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="/root/.gradle/caches/transforms-3/5b75d2e6479f13c5aed7d8001da507c0/transformed/jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
