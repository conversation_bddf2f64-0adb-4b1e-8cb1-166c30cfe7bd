<dependencies>
  <compile
      roots="__local_aars__:/app/build/app/intermediates/flutter/release/libs.jar:unspecified@jar,/app/android@@:flutter_plugin_android_lifecycle::release,/app/android@@:camera_android::release,/app/android@@:tflite_flutter::release,io.flutter:flutter_embedding_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar,androidx.window:window-java:1.0.0-beta04@aar,androidx.window:window:1.0.0-beta04@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.2@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.5.30@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10@jar,io.flutter:armeabi_v7a_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar,io.flutter:arm64_v8a_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar,io.flutter:x86_64_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar,androidx.lifecycle:lifecycle-common-java8:2.2.0@jar,androidx.fragment:fragment:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.activity:activity:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.6.0@aar,androidx.core:core:1.6.0@aar,androidx.lifecycle:lifecycle-runtime:2.2.0@aar,androidx.savedstate:savedstate:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.0.0@aar,androidx.lifecycle:lifecycle-common:2.2.0@jar,androidx.arch.core:core-runtime:2.0.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.lifecycle:lifecycle-viewmodel:2.1.0@aar,androidx.annotation:annotation-jvm:1.7.0@jar,org.jetbrains.kotlin:kotlin-stdlib:1.7.10@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10@jar,org.jetbrains:annotations:13.0@jar,androidx.annotation:annotation-experimental:1.1.0@aar,androidx.tracing:tracing:1.0.0@aar">
    <dependency
        name="__local_aars__:/app/build/app/intermediates/flutter/release/libs.jar:unspecified@jar"
        simpleName="__local_aars__:/app/build/app/intermediates/flutter/release/libs.jar"/>
    <dependency
        name="/app/android@@:flutter_plugin_android_lifecycle::release"
        simpleName="io.flutter.plugins.flutter_plugin_android_lifecycle:flutter_plugin_android_lifecycle"/>
    <dependency
        name="/app/android@@:camera_android::release"
        simpleName="io.flutter.plugins.camera:camera_android"/>
    <dependency
        name="/app/android@@:tflite_flutter::release"
        simpleName="com.tfliteflutter.tflite_flutter_plugin:tflite_flutter"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.window:window-java:1.0.0-beta04@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.window:window:1.0.0-beta04@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.5.30@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.2.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.fragment:fragment:1.1.0@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.activity:activity:1.0.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.6.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.savedstate:savedstate:1.0.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.2.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.0.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.1.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.7.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.1.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
  </compile>
  <package
      roots="__local_aars__:/app/build/app/intermediates/flutter/release/libs.jar:unspecified@jar,/app/android@@:camera_android::release,/app/android@@:flutter_plugin_android_lifecycle::release,/app/android@@:tflite_flutter::release,io.flutter:flutter_embedding_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar,androidx.window:window-java:1.0.0-beta04@aar,androidx.window:window:1.0.0-beta04@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.2@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.5.30@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10@jar,io.flutter:armeabi_v7a_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar,io.flutter:arm64_v8a_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar,io.flutter:x86_64_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar,androidx.lifecycle:lifecycle-common-java8:2.2.0@jar,androidx.fragment:fragment:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.activity:activity:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.6.0@aar,androidx.core:core:1.6.0@aar,androidx.lifecycle:lifecycle-runtime:2.2.0@aar,androidx.savedstate:savedstate:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.0.0@aar,androidx.lifecycle:lifecycle-common:2.2.0@jar,androidx.tracing:tracing:1.0.0@aar,androidx.arch.core:core-runtime:2.0.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.lifecycle:lifecycle-viewmodel:2.1.0@aar,androidx.annotation:annotation-jvm:1.7.0@jar,org.jetbrains.kotlin:kotlin-stdlib:1.7.10@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10@jar,org.jetbrains:annotations:13.0@jar,androidx.annotation:annotation-experimental:1.1.0@aar">
    <dependency
        name="__local_aars__:/app/build/app/intermediates/flutter/release/libs.jar:unspecified@jar"
        simpleName="__local_aars__:/app/build/app/intermediates/flutter/release/libs.jar"/>
    <dependency
        name="/app/android@@:camera_android::release"
        simpleName="io.flutter.plugins.camera:camera_android"/>
    <dependency
        name="/app/android@@:flutter_plugin_android_lifecycle::release"
        simpleName="io.flutter.plugins.flutter_plugin_android_lifecycle:flutter_plugin_android_lifecycle"/>
    <dependency
        name="/app/android@@:tflite_flutter::release"
        simpleName="com.tfliteflutter.tflite_flutter_plugin:tflite_flutter"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.window:window-java:1.0.0-beta04@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.window:window:1.0.0-beta04@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.5.30@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.2.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.fragment:fragment:1.1.0@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.activity:activity:1.0.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.6.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.savedstate:savedstate:1.0.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.2.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.0.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.1.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.7.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.1.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
  </package>
</dependencies>
