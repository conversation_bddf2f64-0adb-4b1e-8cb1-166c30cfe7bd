-- Merging decision tree log ---
application
INJECTED from /app/android/app/src/main/AndroidManifest.xml:8:4-38:19
MERGED from [androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.6.0] /root/.gradle/caches/transforms-3/4ca28c4a71705f8a687c69dda140a277/transformed/core-1.6.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.6.0] /root/.gradle/caches/transforms-3/4ca28c4a71705f8a687c69dda140a277/transformed/core-1.6.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /root/.gradle/caches/transforms-3/5abdc06e026f2f7b9b89c25dcd7a6d7e/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /root/.gradle/caches/transforms-3/5abdc06e026f2f7b9b89c25dcd7a6d7e/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.6.0] /root/.gradle/caches/transforms-3/4ca28c4a71705f8a687c69dda140a277/transformed/core-1.6.0/AndroidManifest.xml:24:18-86
	android:label
		ADDED from /app/android/app/src/main/AndroidManifest.xml:9:9-40
	android:icon
		ADDED from /app/android/app/src/main/AndroidManifest.xml:11:9-43
	android:name
		INJECTED from /app/android/app/src/main/AndroidManifest.xml
		ADDED from /app/android/app/src/main/AndroidManifest.xml:10:9-42
manifest
ADDED from /app/android/app/src/main/AndroidManifest.xml:1:1-39:12
INJECTED from /app/android/app/src/main/AndroidManifest.xml:1:1-39:12
INJECTED from /app/android/app/src/main/AndroidManifest.xml:1:1-39:12
INJECTED from /app/android/app/src/main/AndroidManifest.xml:1:1-39:12
MERGED from [:camera_android] /app/build/camera_android/intermediates/merged_manifest/release/AndroidManifest.xml:2:1-12:12
MERGED from [:flutter_plugin_android_lifecycle] /app/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/release/AndroidManifest.xml:2:1-9:12
MERGED from [:tflite_flutter] /app/build/tflite_flutter/intermediates/merged_manifest/release/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window-java:1.0.0-beta04] /root/.gradle/caches/transforms-3/006d93cd422dbc43e89591147c2de572/transformed/jetified-window-java-1.0.0-beta04/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.fragment:fragment:1.1.0] /root/.gradle/caches/transforms-3/9354257804af8250f2ffb7c22e2892d5/transformed/fragment-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /root/.gradle/caches/transforms-3/f156363817d5f71529d60b6716392550/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /root/.gradle/caches/transforms-3/cf24d35e50a727536c7357721dc007c5/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.0.0] /root/.gradle/caches/transforms-3/252fc848d5aa6e267df5e6907a023aed/transformed/jetified-activity-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] /root/.gradle/caches/transforms-3/f4c2d181f9683c57f8970b9ce7785060/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.6.0] /root/.gradle/caches/transforms-3/4ca28c4a71705f8a687c69dda140a277/transformed/core-1.6.0/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.2.0] /root/.gradle/caches/transforms-3/af4014bd5e02c19d06abc41b9651d21a/transformed/lifecycle-runtime-2.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.0.0] /root/.gradle/caches/transforms-3/95578c0d327ed1d1ac84d08493ede1cf/transformed/jetified-savedstate-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /root/.gradle/caches/transforms-3/7f4e81502f72dd837dc1e0ddcbb11c26/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /root/.gradle/caches/transforms-3/3343d1c6adfed9e861db7c3f7f2df83a/transformed/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.0.0] /root/.gradle/caches/transforms-3/5b75d2e6479f13c5aed7d8001da507c0/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.0.0] /root/.gradle/caches/transforms-3/e5168ae1d5ab03fe8bf4ee87d623deae/transformed/core-runtime-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /root/.gradle/caches/transforms-3/5abdc06e026f2f7b9b89c25dcd7a6d7e/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] /root/.gradle/caches/transforms-3/282676124ae0776b5645a1cee909808d/transformed/lifecycle-viewmodel-2.1.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.1.0] /root/.gradle/caches/transforms-3/8965c941cf4bee4d72541b965c7c9d39/transformed/jetified-annotation-experimental-1.1.0/AndroidManifest.xml:17:1-24:12
INJECTED from /app/android/app/src/main/AndroidManifest.xml:1:1-39:12
INJECTED from /app/android/app/src/main/AndroidManifest.xml:1:1-39:12
INJECTED from /app/android/app/src/main/AndroidManifest.xml:1:1-39:12
	package
		ADDED from /app/android/app/src/main/AndroidManifest.xml:2:5-42
		INJECTED from /app/android/app/src/main/AndroidManifest.xml
		INJECTED from /app/android/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /app/android/app/src/main/AndroidManifest.xml
		ADDED from /app/android/app/src/main/AndroidManifest.xml:1:1-39:12
		INJECTED from /app/android/app/src/main/AndroidManifest.xml
	android:versionCode
		INJECTED from /app/android/app/src/main/AndroidManifest.xml
		ADDED from /app/android/app/src/main/AndroidManifest.xml:1:1-39:12
		INJECTED from /app/android/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /app/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.CAMERA
ADDED from /app/android/app/src/main/AndroidManifest.xml:5:5-65
MERGED from [:camera_android] /app/build/camera_android/intermediates/merged_manifest/release/AndroidManifest.xml:9:5-65
MERGED from [:camera_android] /app/build/camera_android/intermediates/merged_manifest/release/AndroidManifest.xml:9:5-65
	android:name
		ADDED from /app/android/app/src/main/AndroidManifest.xml:5:22-62
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /app/android/app/src/main/AndroidManifest.xml:6:5-81
	android:name
		ADDED from /app/android/app/src/main/AndroidManifest.xml:6:22-78
uses-sdk
INJECTED from /app/android/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /app/android/app/src/main/AndroidManifest.xml
INJECTED from /app/android/app/src/main/AndroidManifest.xml
MERGED from [:camera_android] /app/build/camera_android/intermediates/merged_manifest/release/AndroidManifest.xml:5:5-7:41
MERGED from [:camera_android] /app/build/camera_android/intermediates/merged_manifest/release/AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_plugin_android_lifecycle] /app/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/release/AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_plugin_android_lifecycle] /app/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/release/AndroidManifest.xml:5:5-7:41
MERGED from [:tflite_flutter] /app/build/tflite_flutter/intermediates/merged_manifest/release/AndroidManifest.xml:20:5-22:41
MERGED from [:tflite_flutter] /app/build/tflite_flutter/intermediates/merged_manifest/release/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window-java:1.0.0-beta04] /root/.gradle/caches/transforms-3/006d93cd422dbc43e89591147c2de572/transformed/jetified-window-java-1.0.0-beta04/AndroidManifest.xml:19:5-21:41
MERGED from [androidx.window:window-java:1.0.0-beta04] /root/.gradle/caches/transforms-3/006d93cd422dbc43e89591147c2de572/transformed/jetified-window-java-1.0.0-beta04/AndroidManifest.xml:19:5-21:41
MERGED from [androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] /root/.gradle/caches/transforms-3/9354257804af8250f2ffb7c22e2892d5/transformed/fragment-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] /root/.gradle/caches/transforms-3/9354257804af8250f2ffb7c22e2892d5/transformed/fragment-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] /root/.gradle/caches/transforms-3/f156363817d5f71529d60b6716392550/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /root/.gradle/caches/transforms-3/f156363817d5f71529d60b6716392550/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /root/.gradle/caches/transforms-3/cf24d35e50a727536c7357721dc007c5/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /root/.gradle/caches/transforms-3/cf24d35e50a727536c7357721dc007c5/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.0.0] /root/.gradle/caches/transforms-3/252fc848d5aa6e267df5e6907a023aed/transformed/jetified-activity-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.0.0] /root/.gradle/caches/transforms-3/252fc848d5aa6e267df5e6907a023aed/transformed/jetified-activity-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] /root/.gradle/caches/transforms-3/f4c2d181f9683c57f8970b9ce7785060/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /root/.gradle/caches/transforms-3/f4c2d181f9683c57f8970b9ce7785060/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.6.0] /root/.gradle/caches/transforms-3/4ca28c4a71705f8a687c69dda140a277/transformed/core-1.6.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.6.0] /root/.gradle/caches/transforms-3/4ca28c4a71705f8a687c69dda140a277/transformed/core-1.6.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.2.0] /root/.gradle/caches/transforms-3/af4014bd5e02c19d06abc41b9651d21a/transformed/lifecycle-runtime-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.2.0] /root/.gradle/caches/transforms-3/af4014bd5e02c19d06abc41b9651d21a/transformed/lifecycle-runtime-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.0.0] /root/.gradle/caches/transforms-3/95578c0d327ed1d1ac84d08493ede1cf/transformed/jetified-savedstate-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.0.0] /root/.gradle/caches/transforms-3/95578c0d327ed1d1ac84d08493ede1cf/transformed/jetified-savedstate-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /root/.gradle/caches/transforms-3/7f4e81502f72dd837dc1e0ddcbb11c26/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /root/.gradle/caches/transforms-3/7f4e81502f72dd837dc1e0ddcbb11c26/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /root/.gradle/caches/transforms-3/3343d1c6adfed9e861db7c3f7f2df83a/transformed/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /root/.gradle/caches/transforms-3/3343d1c6adfed9e861db7c3f7f2df83a/transformed/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.0.0] /root/.gradle/caches/transforms-3/5b75d2e6479f13c5aed7d8001da507c0/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /root/.gradle/caches/transforms-3/5b75d2e6479f13c5aed7d8001da507c0/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.0.0] /root/.gradle/caches/transforms-3/e5168ae1d5ab03fe8bf4ee87d623deae/transformed/core-runtime-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] /root/.gradle/caches/transforms-3/e5168ae1d5ab03fe8bf4ee87d623deae/transformed/core-runtime-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /root/.gradle/caches/transforms-3/5abdc06e026f2f7b9b89c25dcd7a6d7e/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /root/.gradle/caches/transforms-3/5abdc06e026f2f7b9b89c25dcd7a6d7e/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] /root/.gradle/caches/transforms-3/282676124ae0776b5645a1cee909808d/transformed/lifecycle-viewmodel-2.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] /root/.gradle/caches/transforms-3/282676124ae0776b5645a1cee909808d/transformed/lifecycle-viewmodel-2.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.1.0] /root/.gradle/caches/transforms-3/8965c941cf4bee4d72541b965c7c9d39/transformed/jetified-annotation-experimental-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] /root/.gradle/caches/transforms-3/8965c941cf4bee4d72541b965c7c9d39/transformed/jetified-annotation-experimental-1.1.0/AndroidManifest.xml:20:5-22:41
INJECTED from /app/android/app/src/main/AndroidManifest.xml
INJECTED from /app/android/app/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /app/android/app/src/main/AndroidManifest.xml
		ADDED from /app/android/app/src/main/AndroidManifest.xml
		INJECTED from /app/android/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /app/android/app/src/main/AndroidManifest.xml
		ADDED from /app/android/app/src/main/AndroidManifest.xml
		INJECTED from /app/android/app/src/main/AndroidManifest.xml
activity#com.example.hello_world_app.MainActivity
ADDED from /app/android/app/src/main/AndroidManifest.xml:12:9-32:20
	android:launchMode
		ADDED from /app/android/app/src/main/AndroidManifest.xml:15:13-43
	android:hardwareAccelerated
		ADDED from /app/android/app/src/main/AndroidManifest.xml:18:13-47
	android:windowSoftInputMode
		ADDED from /app/android/app/src/main/AndroidManifest.xml:19:13-55
	android:exported
		ADDED from /app/android/app/src/main/AndroidManifest.xml:14:13-36
	android:configChanges
		ADDED from /app/android/app/src/main/AndroidManifest.xml:17:13-163
	android:theme
		ADDED from /app/android/app/src/main/AndroidManifest.xml:16:13-47
	android:name
		ADDED from /app/android/app/src/main/AndroidManifest.xml:13:13-41
meta-data#io.flutter.embedding.android.NormalTheme
ADDED from /app/android/app/src/main/AndroidManifest.xml:24:13-27:17
	android:resource
		ADDED from /app/android/app/src/main/AndroidManifest.xml:26:15-52
	android:name
		ADDED from /app/android/app/src/main/AndroidManifest.xml:25:15-70
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /app/android/app/src/main/AndroidManifest.xml:28:13-31:29
action#android.intent.action.MAIN
ADDED from /app/android/app/src/main/AndroidManifest.xml:29:17-68
	android:name
		ADDED from /app/android/app/src/main/AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from /app/android/app/src/main/AndroidManifest.xml:30:17-76
	android:name
		ADDED from /app/android/app/src/main/AndroidManifest.xml:30:27-74
meta-data#flutterEmbedding
ADDED from /app/android/app/src/main/AndroidManifest.xml:35:9-37:33
	android:value
		ADDED from /app/android/app/src/main/AndroidManifest.xml:37:13-30
	android:name
		ADDED from /app/android/app/src/main/AndroidManifest.xml:36:13-44
uses-permission#android.permission.RECORD_AUDIO
ADDED from [:camera_android] /app/build/camera_android/intermediates/merged_manifest/release/AndroidManifest.xml:10:5-71
	android:name
		ADDED from [:camera_android] /app/build/camera_android/intermediates/merged_manifest/release/AndroidManifest.xml:10:22-68
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0-beta04] /root/.gradle/caches/transforms-3/dee59bb480020f42d53c2ea15e4501f7/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:29:13-51
