# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.7.0"
  }
  digests {
    sha256: "\343k\216K\203\223\244\255\307N=J\262*\325\243c\226\360\316\242\344\vW4\352\341I7\337\322$"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.7.10"
  }
  digests {
    sha256: "\347q\376t%\n\224>\217cFq2\001\377\035\214\271\\:]\032\221\242+e\251\340Oj\211\001"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.7.10"
  }
  digests {
    sha256: "\031\361\002\357\351b\237\216\253\3068S\255\025\3053\344|G\371\037\312\t(\\[\336\206\345\237\221\324"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "flutter_embedding_release"
    version: "1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e"
  }
  digests {
    sha256: "\230[D\307,\257\241\223\225\2108\017i\350*\210\345\322\311\031Q\212\272G\3041\246\257Wq\334\320"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.2.0"
  }
  digests {
    sha256: "c\211\215\253\367\317\345\354]~\330\270\302VL\024\'\276\207n\024\226\352\331\\\'\003\317Y\323sK"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\354c\301\273\2274C\313s\035x\3543l^ \347\3565\310\234\2732\323o\222\305[\260%B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "/\206l\a\241\363:\214\233\266\232\225E\324\362\vO\006(\315\n\025T28m|\260\201\341\340\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.1.0"
  }
  digests {
    sha256: "\376\0227\277\002\235\006>\177)\3769\256\257s\357t\310\260\243e\204\206\374)\323\305C&e8\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.1.0"
  }
  digests {
    sha256: "\241L\213\217!S\361(\350\000\373\322f\246\276\253\034(9\202\242\236\305p\322\314\005\323\a\330\024\226"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.6.0"
  }
  digests {
    sha256: "\207]\276\310\210\311\033\005R\025u\375\030\f\300\210$\322\304\321+\020\260&\033O\235h%!N\361"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.1.0"
  }
  digests {
    sha256: "\001W\336a\242\006@G\211j\005\200\200\363\375g\272W\255\232\224\205{?z66`$>?\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.0.0"
  }
  digests {
    sha256: "\310&\t\316\330\304\230\360\247\001\243\017\266w\033\267H\b`\332\356\204\330.\n\201\356\206\355\367\2729"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.0.0"
  }
  digests {
    sha256: "\207\346_\307g\307\022\2647d\234|\356$1\353\264\276\326\332\357\202\345\001\324\022[>\323\366_\216"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.0.0"
  }
  digests {
    sha256: "\375\3434\354~\"tL\017[\376|\257\032\204\311\327\0272pD@\005w\275\371\275\222\036\304\367\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.1.0"
  }
  digests {
    sha256: "\272U\373z\301\262\202\215S\'\315\250\254\367\b]\231\v+LC\3573l\252ghbI\270R="
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.0.0"
  }
  digests {
    sha256: "\321\274\230BE\\.SD\025\330\214D\337MRA;G\215\271\t:\033\243c$\367\005\364L="
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.0.0"
  }
  digests {
    sha256: "%\020\245a\2347W\234\234\341\240Et\372\2572<\320\377\342\374N \372\217\217\001\345\273@.\203"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window-java"
    version: "1.0.0-beta04"
  }
  digests {
    sha256: "\034&i\247_`\027\275E\344C6\037(\302\204(e+\230_e\361[\205\253_\347\222\247\270\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.5.2"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.5.2"
  }
  digests {
    sha256: "#\024\304\212\241\243\234\005\301\037;8\\\337<V\261t_\006C\024\344\376\253\354\353\301W+:\301"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.5.30"
  }
  digests {
    sha256: "\263\254\240S\234X2b#\260}\307\300\254y\265\250u\272\200\313\221\356\026\t\354\211\2737\270\231\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.7.10"
  }
  digests {
    sha256: "T\366\023Q\261\223j\330\217NS\005\237\347\201\347#\352\345\035x\355\236t\"\330\264\003WN\306\202"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.0.0-beta04"
  }
  digests {
    sha256: "\315\2257\241x\364\2457\241\247\337\320hJ\311\256\271|D\031\235\263|@\354\274\315J|]\340\025"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.5.2"
  }
  digests {
    sha256: "\206\317\230\222\260\275S\006\250\364\327\255\212\2025oaM\307\325\031\3530c\260\210}|+@Y("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "armeabi_v7a_release"
    version: "1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e"
  }
  digests {
    sha256: "\311\346\326\331I,\375;\030\243\367\002\371\260\274H\212\345\034S\320_\220t\323\306Jo\2508\300\303"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "arm64_v8a_release"
    version: "1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e"
  }
  digests {
    sha256: "\357\326\023\274\031R\200\221\272\215\210\027\242\230W\377\237\250u\372\325\024)\030\177\202\221\362%\323\206\341"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "x86_64_release"
    version: "1.0.0-1837b5be5f0f1376a1ccf383950e83a80177fb4e"
  }
  digests {
    sha256: "|\200z\243E\376Rh%\243H\rC\275.AfxDQf\222\202\337\313\322\254\224\363d\037\270"
  }
  repo_index {
    value: 2
  }
}
library {
  digests {
    sha256: "\202\362\254D\264\r\023\255!\355,\254n\nMv\271\237\270\b-\301\232\336L\326\030\261L\031\005e"
  }
}
library_dependencies {
  library_dep_index: 1
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 11
  library_dep_index: 25
}
library_dependencies {
  library_index: 6
  library_dep_index: 0
}
library_dependencies {
  library_index: 7
  library_dep_index: 6
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 0
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 21
}
library_dependencies {
  library_index: 11
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 14
}
library_dependencies {
  library_index: 13
  library_dep_index: 0
  library_dep_index: 14
}
library_dependencies {
  library_index: 14
  library_dep_index: 0
}
library_dependencies {
  library_index: 15
  library_dep_index: 0
  library_dep_index: 11
  library_dep_index: 16
}
library_dependencies {
  library_index: 16
  library_dep_index: 0
  library_dep_index: 11
}
library_dependencies {
  library_index: 17
  library_dep_index: 0
  library_dep_index: 11
  library_dep_index: 18
  library_dep_index: 21
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 9
}
library_dependencies {
  library_index: 19
  library_dep_index: 0
  library_dep_index: 9
}
library_dependencies {
  library_index: 20
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 19
}
library_dependencies {
  library_index: 21
  library_dep_index: 0
}
library_dependencies {
  library_index: 22
  library_dep_index: 0
  library_dep_index: 11
  library_dep_index: 8
  library_dep_index: 21
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 0
  library_dep_index: 9
  library_dep_index: 6
}
library_dependencies {
  library_index: 24
  library_dep_index: 0
}
library_dependencies {
  library_index: 25
  library_dep_index: 2
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 11
}
library_dependencies {
  library_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 27
  library_dep_index: 28
  library_dep_index: 3
}
library_dependencies {
  library_index: 28
  library_dep_index: 2
  library_dep_index: 29
}
library_dependencies {
  library_index: 29
  library_dep_index: 2
}
library_dependencies {
  library_index: 30
  library_dep_index: 2
  library_dep_index: 31
  library_dep_index: 0
  library_dep_index: 14
  library_dep_index: 11
}
library_dependencies {
  library_index: 31
  library_dep_index: 26
  library_dep_index: 28
}
module_dependencies {
  module_name: "base"
  dependency_index: 29
  dependency_index: 32
  dependency_index: 33
  dependency_index: 34
  dependency_index: 0
  dependency_index: 5
  dependency_index: 35
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://storage.googleapis.com/download.flutter.io"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jcenter.bintray.com/"
  }
}
