{"logs": [{"outputFile": "io.flutter.plugins.flutter_plugin_android_lifecycle-merged_res-4:/values-de/values-de.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/4ca28c4a71705f8a687c69dda140a277/transformed/core-1.6.0/res/values-de/values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "io.flutter.plugins.flutter_plugin_android_lifecycle-mergeReleaseResources-2:/values-de/values-de.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/4ca28c4a71705f8a687c69dda140a277/transformed/core-1.6.0/res/values-de/values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}]}