1<?xml version="1.0" encoding="utf-8"?>
2<!--
3    ~ Copyright 2023 The TensorFlow Authors. All Rights Reserved.
4    ~
5    ~ Licensed under the Apache License, Version 2.0 (the "License");
6    ~ you may not use this file except in compliance with the License.
7    ~ You may obtain a copy of the License at
8    ~
9    ~       http://www.apache.org/licenses/LICENSE-2.0
10    ~
11    ~ Unless required by applicable law or agreed to in writing, software
12    ~ distributed under the License is distributed on an "AS IS" BASIS,
13    ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
14    ~ See the License for the specific language governing permissions and
15    ~ limitations under the License.
16-->
17<manifest xmlns:android="http://schemas.android.com/apk/res/android"
18    package="com.tfliteflutter.tflite_flutter_plugin" >
19
20    <uses-sdk
21        android:minSdkVersion="16"
21-->/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.9.5/android/src/main/AndroidManifest.xml
22        android:targetSdkVersion="16" />
22-->/root/.pub-cache/hosted/pub.dev/tflite_flutter-0.9.5/android/src/main/AndroidManifest.xml
23
24</manifest>
